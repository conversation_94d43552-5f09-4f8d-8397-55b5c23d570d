{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DGCNN Pile Detection\n", "\n", "This notebook implements Dynamic Graph CNN (DGCNN) architecture for pile detection using the same training data as PointNet++.\n", "\n", "**Architecture:**\n", "- Dynamic graph construction with k-nearest neighbors\n", "- EdgeConv operations for local feature aggregation\n", "- Multi-scale feature extraction\n", "- Binary classification (pile vs non-pile)\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add notebooks to path\n", "notebooks_root = Path.cwd().parents[4]\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Parameters\n", "training_data_path = \"../../rule_based/data/output_runs\"  # Path to IFC training data\n", "batch_size = 16\n", "num_epochs = 100\n", "learning_rate = 0.001\n", "num_points = 1024  # Points per patch\n", "k = 20  # Number of nearest neighbors\n", "save_model = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import json\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "print(\"=== DGCNN PILE DETECTION ===\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading (Reuse from PointNet++)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_training_data(data_dir):\n", "    \"\"\"Load training data from IFC rule-based detection\"\"\"\n", "    data_dir = Path(data_dir)\n", "    \n", "    # Find latest training data\n", "    training_files = list(data_dir.glob(\"*/training_data.json\"))\n", "    if not training_files:\n", "        raise FileNotFoundError(f\"No training data found in {data_dir}\")\n", "    \n", "    latest_file = max(training_files, key=lambda x: x.parent.name)\n", "    print(f\"Loading training data: {latest_file}\")\n", "    \n", "    with open(latest_file, 'r') as f:\n", "        data = json.load(f)\n", "    \n", "    return data\n", "\n", "def preprocess_patches(patches, num_points=1024):\n", "    \"\"\"Preprocess point patches for DGCNN\"\"\"\n", "    processed = []\n", "    \n", "    for patch in patches:\n", "        patch_array = np.array(patch)\n", "        \n", "        if len(patch_array) == 0:\n", "            continue\n", "            \n", "        # Sample or pad to fixed number of points\n", "        if len(patch_array) >= num_points:\n", "            # Random sampling\n", "            indices = np.random.choice(len(patch_array), num_points, replace=False)\n", "            sampled_patch = patch_array[indices]\n", "        else:\n", "            # Pad with repetition\n", "            repetitions = num_points // len(patch_array) + 1\n", "            repeated = np.tile(patch_array, (repetitions, 1))\n", "            sampled_patch = repeated[:num_points]\n", "        \n", "        # Normalize coordinates\n", "        centroid = np.mean(sampled_patch, axis=0)\n", "        sampled_patch = sampled_patch - centroid\n", "        \n", "        # Scale to unit sphere\n", "        max_dist = np.max(np.linalg.norm(sampled_patch, axis=1))\n", "        if max_dist > 0:\n", "            sampled_patch = sampled_patch / max_dist\n", "        \n", "        processed.append(sampled_patch)\n", "    \n", "    return np.array(processed)\n", "\n", "class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "    \n", "    def __len__(self):\n", "        return len(self.points)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n", "\n", "# Load and preprocess data\n", "training_data = load_training_data(training_data_path)\n", "\n", "positive_patches = preprocess_patches(training_data['positive_patches'], num_points)\n", "negative_patches = preprocess_patches(training_data['negative_patches'], num_points)\n", "\n", "print(f\"Positive patches: {len(positive_patches)}\")\n", "print(f\"Negative patches: {len(negative_patches)}\")\n", "\n", "# Combine data\n", "X = np.concatenate([positive_patches, negative_patches], axis=0)\n", "y = np.concatenate([np.ones(len(positive_patches)), np.zeros(len(negative_patches))], axis=0)\n", "\n", "print(f\"Total samples: {len(X)}\")\n", "print(f\"Input shape: {X.shape}\")\n", "\n", "# Split data\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_train, y_train, test_size=0.2, random_state=42, stratify=y_train\n", ")\n", "\n", "print(f\"Train: {len(X_train)}, Val: {len(X_val)}, Test: {len(X_test)}\")\n", "\n", "# Create datasets and dataloaders\n", "train_dataset = PileDataset(X_train, y_train)\n", "val_dataset = PileDataset(X_val, y_val)\n", "test_dataset = PileDataset(X_test, y_test)\n", "\n", "train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## DGCNN Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2 * torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    \n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]  # (batch_size, num_points, k)\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Construct graph features\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    \n", "    if idx is None:\n", "        idx = knn(x, k=k)  # (batch_size, num_points, k)\n", "    \n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "    \n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", "    \n", "    _, num_dims, _ = x.size()\n", "    \n", "    x = x.transpose(2, 1).contiguous()  # (batch_size, num_points, num_dims)\n", "    feature = x.view(batch_size * num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "    \n", "    return feature  # (batch_size, 2*num_dims, num_points, k)\n", "\n", "class EdgeConv(nn.Module):\n", "    def __init__(self, in_channels, out_channels, k=20):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.conv = nn.Sequential(\n", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),\n", "            nn.BatchNorm2d(out_channels),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv(x)\n", "        x = x.max(dim=-1, keepdim=False)[0]\n", "        return x\n", "\n", "class DGCNN(nn.Module):\n", "    def __init__(self, num_classes=2, k=20):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        \n", "        # EdgeConv layers\n", "        self.conv1 = EdgeConv(3, 64, k)\n", "        self.conv2 = EdgeConv(64, 64, k)\n", "        self.conv3 = EdgeConv(64, 128, k)\n", "        self.conv4 = EdgeConv(128, 256, k)\n", "        \n", "        # Global feature aggregation\n", "        self.conv5 = nn.Sequential(\n", "            nn.Conv1d(512, 1024, kernel_size=1, bias=False),\n", "            nn.<PERSON>ch<PERSON>orm1d(1024),\n", "            nn.LeakyReLU(negative_slope=0.2)\n", "        )\n", "        \n", "        # Classification head\n", "        self.classifier = nn.Sequential(\n", "            nn.<PERSON>(1024, 512),\n", "            nn.BatchNorm1d(512),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.5),\n", "            nn.<PERSON><PERSON>(512, 256),\n", "            nn.BatchNorm1d(256),\n", "            nn.LeakyReLU(negative_slope=0.2),\n", "            nn.Dropout(0.5),\n", "            nn.Linear(256, num_classes)\n", "        )\n", "    \n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        x = x.transpose(2, 1)  # (batch_size, 3, num_points)\n", "        \n", "        # EdgeConv layers\n", "        x1 = self.conv1(x)\n", "        x2 = self.conv2(x1)\n", "        x3 = self.conv3(x2)\n", "        x4 = self.conv4(x3)\n", "        \n", "        # Concatenate features\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)\n", "        \n", "        # Global feature\n", "        x = self.conv5(x)\n", "        x = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)\n", "        \n", "        # Classification\n", "        x = self.classifier(x)\n", "        \n", "        return x\n", "\n", "# Initialize model\n", "model = DGCNN(num_classes=2, k=k).to(device)\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "print(f\"DGCNN model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "print(f\"Using k={k} nearest neighbors\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train_epoch(model, loader, criterion, optimizer, device):\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    for batch_idx, (data, target) in enumerate(loader):\n", "        data, target = data.to(device), target.to(device)\n", "        \n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "    \n", "    return total_loss / len(loader), correct / total\n", "\n", "def validate_epoch(model, loader, criterion, device):\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "    \n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            \n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "    \n", "    return total_loss / len(loader), correct / total\n", "\n", "# Training loop\n", "print(\"Starting DGCNN training...\")\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "best_val_acc = 0\n", "patience = 10\n", "patience_counter = 0\n", "\n", "for epoch in range(num_epochs):\n", "    train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)\n", "    val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)\n", "    \n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "    \n", "    if (epoch + 1) % 10 == 0:\n", "        print(f\"Epoch {epoch+1}/{num_epochs}:\")\n", "        print(f\"  Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}\")\n", "        print(f\"  Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}\")\n", "    \n", "    # Early stopping\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        patience_counter = 0\n", "        if save_model:\n", "            torch.save(model.state_dict(), 'best_dgcnn.pth')\n", "    else:\n", "        patience_counter += 1\n", "        if patience_counter >= patience:\n", "            print(f\"Early stopping at epoch {epoch+1}\")\n", "            break\n", "\n", "print(f\"DGCNN training completed. Best validation accuracy: {best_val_acc:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluation and Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load best model for evaluation\n", "if save_model:\n", "    model.load_state_dict(torch.load('best_dgcnn.pth'))\n", "\n", "# Test evaluation\n", "model.eval()\n", "all_preds = []\n", "all_targets = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "        pred = output.argmax(dim=1)\n", "        \n", "        all_preds.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_accuracy = accuracy_score(all_targets, all_preds)\n", "test_precision = precision_score(all_targets, all_preds)\n", "test_recall = recall_score(all_targets, all_preds)\n", "test_f1 = f1_score(all_targets, all_preds)\n", "\n", "print(\"=== DGCNN TEST RESULTS ===\")\n", "print(f\"Accuracy: {test_accuracy:.4f}\")\n", "print(f\"Precision: {test_precision:.4f}\")\n", "print(f\"Recall: {test_recall:.4f}\")\n", "print(f\"F1-Score: {test_f1:.4f}\")\n", "\n", "# Compare with baselines\n", "rule_based_performance = training_data['performance']\n", "\n", "# Load PointNet++ results if available\n", "pointnet_results_file = '../pointnet_plus_plus/pointnet_plus_plus_results.json'\n", "pointnet_f1 = None\n", "if Path(pointnet_results_file).exists():\n", "    with open(pointnet_results_file, 'r') as f:\n", "        pointnet_results = json.load(f)\n", "    pointnet_f1 = pointnet_results['test_metrics']['f1_score']\n", "\n", "print(\"\\n=== COMPREHENSIVE COMPARISON ===\")\n", "print(f\"Rule-based F1: {rule_based_performance['f1_score']:.4f}\")\n", "if pointnet_f1:\n", "    print(f\"PointNet++ F1: {pointnet_f1:.4f}\")\n", "print(f\"DGCNN F1: {test_f1:.4f}\")\n", "\n", "# Calculate improvements\n", "rule_improvement = ((test_f1 - rule_based_performance['f1_score']) / rule_based_performance['f1_score']) * 100\n", "print(f\"\\nDGCNN vs Rule-based improvement: {rule_improvement:.1f}%\")\n", "\n", "if pointnet_f1:\n", "    pointnet_comparison = ((test_f1 - pointnet_f1) / pointnet_f1) * 100\n", "    print(f\"DGCNN vs PointNet++ improvement: {pointnet_comparison:.1f}%\")\n", "    \n", "    if test_f1 > pointnet_f1:\n", "        print(\"DGCNN outperforms PointNet++\")\n", "    elif test_f1 < pointnet_f1:\n", "        print(\"PointNet++ outperforms DGCNN\")\n", "    else:\n", "        print(\"DGCNN and PointNet++ perform similarly\")\n", "\n", "# Save results\n", "results = {\n", "    'model': 'DGCNN',\n", "    'test_metrics': {\n", "        'accuracy': float(test_accuracy),\n", "        'precision': float(test_precision),\n", "        'recall': float(test_recall),\n", "        'f1_score': float(test_f1)\n", "    },\n", "    'training_info': {\n", "        'num_epochs': len(train_losses),\n", "        'best_val_acc': float(best_val_acc),\n", "        'final_train_acc': float(train_accs[-1]),\n", "        'final_val_acc': float(val_accs[-1]),\n", "        'k_neighbors': k\n", "    },\n", "    'comparison': {\n", "        'rule_based_f1': rule_based_performance['f1_score'],\n", "        'dgcnn_f1': float(test_f1),\n", "        'rule_improvement_percent': float(rule_improvement)\n", "    }\n", "}\n", "\n", "if pointnet_f1:\n", "    results['comparison']['pointnet_plus_plus_f1'] = pointnet_f1\n", "    results['comparison']['pointnet_comparison_percent'] = float(pointnet_comparison)\n", "\n", "with open('dgcnn_results.json', 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"\\nDGCNN results saved to dgcnn_results.json\")\n", "print(\"\\n=== MODELING COMPLETE ===\")\n", "print(\"All three approaches (Rule-based, PointNet++, DGCNN) have been implemented and compared.\")\n", "print(\"Ready for final analysis and dissertation documentation.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}