# %%
"""
Cluster Data Exporter
Export cluster information and coordinates for use in external tools
"""

import numpy as np
import open3d as o3d
import pandas as pd
from pathlib import Path
import json
import csv

# Configuration
CLUSTER_DIR = Path("dbscan_clusters")
LABELS_DIR = Path("cluster_labels")
METADATA_FILE = "labeling_metadata.json"
OUTPUT_DIR = Path("cluster_exports")

CLASS_NAMES = {0: 'pile', 1: 'support', 2: 'pallet', 3: 'ground', 4: 'noise', 5: 'unlabeled'}

def load_cluster_data(cluster_name):
    """Load cluster points and labels"""
    cluster_file = CLUSTER_DIR / f"{cluster_name}.ply"
    pcd = o3d.io.read_point_cloud(str(cluster_file))
    points = np.asarray(pcd.points)
    
    label_file = LABELS_DIR / f"{cluster_name}.npy"
    labels = np.load(label_file) if label_file.exists() else None
    
    return points, labels

def export_cluster_summary():
    """Export summary table of all clusters"""
    metadata_path = LABELS_DIR / METADATA_FILE
    if not metadata_path.exists():
        print("No metadata found")
        return
    
    with open(metadata_path, 'r') as f:
        all_metadata = json.load(f)
    
    # Create summary data
    summary_data = []
    
    for cluster_name, meta in all_metadata.items():
        row = {
            'cluster_name': cluster_name,
            'num_points': meta.get('num_points', 0),
            'height_m': round(meta.get('height', 0), 2),
            'category': meta.get('category', 'unknown'),
            'auto_labeled': meta.get('auto_labeled', False)
        }
        
        # Add label distribution
        label_dist = meta.get('label_distribution', {})
        for class_name in ['pile', 'ground', 'support', 'pallet', 'noise']:
            if class_name in label_dist:
                row[f'{class_name}_points'] = label_dist[class_name]['count']
                row[f'{class_name}_percent'] = round(label_dist[class_name]['percentage'], 1)
            else:
                row[f'{class_name}_points'] = 0
                row[f'{class_name}_percent'] = 0.0
        
        summary_data.append(row)
    
    # Convert to DataFrame and sort
    df = pd.DataFrame(summary_data)
    df = df.sort_values('cluster_name')
    
    # Export to CSV
    OUTPUT_DIR.mkdir(exist_ok=True)
    summary_file = OUTPUT_DIR / "cluster_summary.csv"
    df.to_csv(summary_file, index=False)
    
    print(f"Exported cluster summary to: {summary_file}")
    print(f"Contains {len(df)} clusters")
    
    # Show some statistics
    print(f"\nSummary Statistics:")
    print(f"Total points across all clusters: {df['num_points'].sum():,}")
    print(f"Average height: {df['height_m'].mean():.2f}m")
    print(f"Height range: {df['height_m'].min():.2f}m to {df['height_m'].max():.2f}m")
    
    # Category breakdown
    print(f"\nBy category:")
    category_counts = df['category'].value_counts()
    for category, count in category_counts.items():
        avg_height = df[df['category'] == category]['height_m'].mean()
        print(f"  {category}: {count} clusters (avg height: {avg_height:.2f}m)")
    
    # Show clusters that are NOT pure pile
    non_pile = df[df['pile_percent'] < 100]
    if len(non_pile) > 0:
        print(f"\nClusters with mixed/non-pile content:")
        for _, row in non_pile.iterrows():
            print(f"  {row['cluster_name']}: {row['pile_percent']:.1f}% pile")
    
    return df

def export_specific_clusters(cluster_names, include_coordinates=True):
    """Export detailed data for specific clusters"""
    OUTPUT_DIR.mkdir(exist_ok=True)
    
    for cluster_name in cluster_names:
        print(f"\nExporting {cluster_name}...")
        
        try:
            points, labels = load_cluster_data(cluster_name)
            
            if include_coordinates:
                # Create detailed CSV with coordinates and labels
                data = {
                    'x': points[:, 0],
                    'y': points[:, 1], 
                    'z': points[:, 2],
                    'height_above_min': points[:, 2] - points[:, 2].min()
                }
                
                if labels is not None:
                    data['label_id'] = labels
                    data['label_name'] = [CLASS_NAMES.get(label, f'class_{label}') for label in labels]
                
                df = pd.DataFrame(data)
                
                # Export coordinates CSV
                coord_file = OUTPUT_DIR / f"{cluster_name}_coordinates.csv"
                df.to_csv(coord_file, index=False)
                print(f"  Coordinates saved to: {coord_file}")
                
            # Create summary text file
            summary_file = OUTPUT_DIR / f"{cluster_name}_summary.txt"
            with open(summary_file, 'w') as f:
                f.write(f"CLUSTER ANALYSIS: {cluster_name}\n")
                f.write("="*50 + "\n\n")
                
                f.write(f"Basic Information:\n")
                f.write(f"  Total points: {len(points):,}\n")
                f.write(f"  X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f} ({points[:, 0].max()-points[:, 0].min():.2f}m span)\n")
                f.write(f"  Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f} ({points[:, 1].max()-points[:, 1].min():.2f}m span)\n")
                f.write(f"  Z range: {points[:, 2].min():.2f} to {points[:, 2].max():.2f} ({points[:, 2].max()-points[:, 2].min():.2f}m height)\n\n")
                
                if labels is not None:
                    f.write(f"Label Distribution:\n")
                    unique_labels, counts = np.unique(labels, return_counts=True)
                    for label, count in zip(unique_labels, counts):
                        class_name = CLASS_NAMES.get(label, f'class_{label}')
                        pct = (count / len(labels)) * 100
                        f.write(f"  {class_name}: {count:,} points ({pct:.1f}%)\n")
                    
                    f.write(f"\nSpatial Analysis by Label:\n")
                    for label in unique_labels:
                        mask = labels == label
                        if np.sum(mask) > 0:
                            label_points = points[mask]
                            class_name = CLASS_NAMES.get(label, f'class_{label}')
                            f.write(f"  {class_name}:\n")
                            f.write(f"    Points: {np.sum(mask):,}\n")
                            f.write(f"    Height range: {label_points[:, 2].min():.2f} to {label_points[:, 2].max():.2f}m\n")
                            f.write(f"    Average height: {label_points[:, 2].mean():.2f}m\n")
                            f.write(f"    Height std: {label_points[:, 2].std():.2f}m\n")
                
            print(f"  Summary saved to: {summary_file}")
            
        except Exception as e:
            print(f"  Error exporting {cluster_name}: {e}")

def list_pile_numbers():
    """List all pile locations with their properties"""
    metadata_path = LABELS_DIR / METADATA_FILE
    if not metadata_path.exists():
        print("No metadata found")
        return
    
    with open(metadata_path, 'r') as f:
        all_metadata = json.load(f)
    
    print(f"PILE INVENTORY")
    print("="*60)
    
    pile_data = []
    for cluster_name, meta in all_metadata.items():
        # Extract pile number from cluster name
        pile_num = cluster_name.replace('cluster_', '')
        
        pile_info = {
            'pile_number': pile_num,
            'total_points': meta.get('num_points', 0),
            'height_m': round(meta.get('height', 0), 2),
            'category': meta.get('category', 'unknown')
        }
        
        # Get pile content percentage
        label_dist = meta.get('label_distribution', {})
        pile_pct = label_dist.get('pile', {}).get('percentage', 0)
        pile_points = label_dist.get('pile', {}).get('count', 0)
        
        pile_info['pile_percentage'] = round(pile_pct, 1)
        pile_info['pile_points'] = pile_points
        
        # Determine pile status
        if pile_pct >= 90:
            status = "Pure pile"
        elif pile_pct >= 50:
            status = "Mostly pile"
        elif pile_pct > 0:
            status = "Some pile"
        else:
            status = "No pile"
        
        pile_info['status'] = status
        pile_data.append(pile_info)
    
    # Sort by pile number
    pile_data.sort(key=lambda x: int(x['pile_number']))
    
    # Print table
    print(f"{'Pile#':<6} {'Height':<8} {'Points':<8} {'Pile%':<8} {'Status':<12} {'Category':<15}")
    print("-" * 65)
    
    for pile in pile_data:
        print(f"{pile['pile_number']:<6} {pile['height_m']:<8} {pile['total_points']:<8} {pile['pile_percentage']:<8} {pile['status']:<12} {pile['category']:<15}")
    
    # Summary stats
    print(f"\nSUMMARY:")
    print(f"Total pile locations: {len(pile_data)}")
    
    status_counts = {}
    for pile in pile_data:
        status = pile['status']
        status_counts[status] = status_counts.get(status, 0) + 1
    
    for status, count in status_counts.items():
        print(f"  {status}: {count} locations")
    
    # Save to file
    OUTPUT_DIR.mkdir(exist_ok=True)
    pile_list_file = OUTPUT_DIR / "pile_inventory.txt"
    with open(pile_list_file, 'w') as f:
        f.write("PILE INVENTORY\n")
        f.write("="*60 + "\n\n")
        f.write(f"{'Pile#':<6} {'Height':<8} {'Points':<8} {'Pile%':<8} {'Status':<12} {'Category':<15}\n")
        f.write("-" * 65 + "\n")
        for pile in pile_data:
            f.write(f"{pile['pile_number']:<6} {pile['height_m']:<8} {pile['total_points']:<8} {pile['pile_percentage']:<8} {pile['status']:<12} {pile['category']:<15}\n")
    
    print(f"\nPile inventory saved to: {pile_list_file}")
    
    return pile_data

def export_for_external_tools():
    """Export data in formats suitable for external visualization tools"""
    OUTPUT_DIR.mkdir(exist_ok=True)
    
    print("Choose export format:")
    print("1. CSV summary (for Excel, Google Sheets)")
    print("2. Pile inventory list")
    print("3. Detailed coordinates for specific clusters")
    print("4. All of the above")
    
    choice = input("Choice (1-4): ").strip()
    
    if choice in ['1', '4']:
        print("\nExporting CSV summary...")
        export_cluster_summary()
    
    if choice in ['2', '4']:
        print("\nGenerating pile inventory...")
        list_pile_numbers()
    
    if choice in ['3', '4']:
        print("\nWhich clusters do you want detailed coordinates for?")
        print("Examples: cluster_000,cluster_111,cluster_045")
        print("Or enter 'mixed' for clusters with multiple labels")
        print("Or enter 'sample' for 5 random clusters")
        
        cluster_input = input("Clusters: ").strip()
        
        if cluster_input.lower() == 'mixed':
            # Find mixed clusters
            metadata_path = LABELS_DIR / METADATA_FILE
            with open(metadata_path, 'r') as f:
                all_metadata = json.load(f)
            
            mixed_clusters = []
            for name, meta in all_metadata.items():
                label_dist = meta.get('label_distribution', {})
                if len(label_dist) > 1:
                    mixed_clusters.append(name)
            
            if mixed_clusters:
                print(f"Found {len(mixed_clusters)} mixed clusters: {mixed_clusters}")
                export_specific_clusters(mixed_clusters)
            else:
                print("No mixed clusters found")
                
        elif cluster_input.lower() == 'sample':
            metadata_path = LABELS_DIR / METADATA_FILE
            with open(metadata_path, 'r') as f:
                all_metadata = json.load(f)
            
            sample_clusters = np.random.choice(list(all_metadata.keys()), 5, replace=False)
            print(f"Sampling: {list(sample_clusters)}")
            export_specific_clusters(sample_clusters)
            
        else:
            cluster_names = [name.strip() for name in cluster_input.split(',')]
            export_specific_clusters(cluster_names)
    
    print(f"\nAll exports saved to: {OUTPUT_DIR}/")
    print("You can now use these files in Excel, GIS software, or other visualization tools!")

# %%
if __name__ == "__main__":
    print("=== CLUSTER DATA EXPORTER ===")
    print("Export cluster data for use in external visualization tools")
    
    export_for_external_tools()