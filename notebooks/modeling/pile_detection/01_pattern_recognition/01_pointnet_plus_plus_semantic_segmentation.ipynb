{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PointNet++ Semantic Segmentation for Pile Clusters\n", "\n", "This notebook implements **PointNet++** for semantic segmentation within DBSCAN clusters to classify points into:\n", "- **pile**: Main foundation pile structure\n", "- **support**: Support structures/scaffolding\n", "- **pallet**: Storage pallets\n", "- **non-pile**: Ground, debris, other objects\n", "\n", "**PointNet++** provides hierarchical feature learning and better geometric understanding compared to PointNet.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis - Semantic Segmentation Stage\n", "\n", "## Learning Objectives\n", "1. Understand PointNet++ architecture components\n", "2. Learn hierarchical point cloud processing\n", "3. Implement semantic segmentation for construction elements\n", "4. Analyze pile detection results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries and Setup\n", "\n", "Let's start by importing the necessary libraries for deep learning, point cloud processing, and visualization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "import seaborn as sns\n", "from datetime import datetime\n", "import pickle\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set device for computation\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. PointNet++ Core Functions\n", "\n", "PointNet++ uses several key operations for hierarchical point processing. Let's implement these step by step."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Distance and Indexing Functions\n", "\n", "These functions help us calculate distances between points and index them efficiently."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def square_distance(src, dst):\n", "    \"\"\"Calculate Euclidean distance between each two points\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def index_points(points, idx):\n", "    \"\"\"Index points according to the indices\"\"\"\n", "    device = points.device\n", "    B = points.shape[0]\n", "    view_shape = list(idx.shape)\n", "    view_shape[1:] = [1] * (len(view_shape) - 1)\n", "    repeat_shape = list(idx.shape)\n", "    repeat_shape[0] = 1\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)\n", "    new_points = points[batch_indices, idx, :]\n", "    return new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Farthest Point Sampling\n", "\n", "This function selects representative points that are spread out across the point cloud."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest Point Sampling - selects well-distributed points\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Ball Query\n", "\n", "This function finds neighboring points within a specified radius."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"Ball query - find neighbors within radius\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.4 Sampling and Grouping Functions\n", "\n", "These functions combine sampling and grouping operations for hierarchical processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sample_and_group(npoint, radius, nsample, xyz, points, returnfps=False):\n", "    \"\"\"Sample and group points for hierarchical processing\"\"\"\n", "    B, N, C = xyz.shape\n", "    S = npoint\n", "    \n", "    # Sample points using farthest point sampling\n", "    fps_idx = farthest_point_sample(xyz, npoint)  # [B, npoint]\n", "    new_xyz = index_points(xyz, fps_idx)\n", "    \n", "    # Group neighboring points\n", "    idx = query_ball_point(radius, nsample, xyz, new_xyz)\n", "    grouped_xyz = index_points(xyz, idx)  # [B, npoint, nsample, C]\n", "    grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "    \n", "    if points is not None:\n", "        grouped_points = index_points(points, idx)\n", "        new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # [B, npoint, nsample, C+D]\n", "    else:\n", "        new_points = grouped_xyz_norm\n", "    \n", "    if returnfps:\n", "        return new_xyz, new_points, grouped_xyz, fps_idx\n", "    else:\n", "        return new_xyz, new_points\n", "\n", "def sample_and_group_all(xyz, points):\n", "    \"\"\"Group all points (used in final layer)\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    new_xyz = torch.zeros(B, 1, C).to(device)\n", "    grouped_xyz = xyz.view(B, 1, N, C)\n", "    if points is not None:\n", "        new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "    else:\n", "        new_points = grouped_xyz\n", "    return new_xyz, new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. PointNet++ Architecture Components\n", "\n", "Now let's build the main components of the PointNet++ architecture."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Set Abstraction Layer\n", "\n", "This layer reduces the number of points while extracting features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    \"\"\"PointNet Set Abstraction Layer\"\"\"\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        \n", "        # Build MLP layers\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        \"\"\"\n", "        Input:\n", "            xyz: input points position data, [B, C, N]\n", "            points: input points data, [B, D, N]\n", "        Return:\n", "            new_xyz: sampled points position data, [B, C, S]\n", "            new_points_concat: sample points feature data, [B, D', S]\n", "        \"\"\"\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = sample_and_group(self.npoint, self.radius, self.nsample, xyz, points)\n", "        \n", "        # Apply MLP to grouped points\n", "        new_points = new_points.permute(0, 3, 2, 1)  # [B, C+D, nsample, npoint]\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        # Max pooling to get point-wise features\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Feature Propagation Layer\n", "\n", "This layer upsamples features back to the original resolution."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class PointNetFeaturePropagation(nn.Module):\n", "    \"\"\"Feature Propagation Layer for PointNet++\"\"\"\n", "    def __init__(self, in_channel, mlp):\n", "        super(PointNetFeaturePropagation, self).__init__()\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        \n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm1d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz1, xyz2, points1, points2):\n", "        \"\"\"\n", "        Input:\n", "            xyz1: input points position data, [B, C, N]\n", "            xyz2: sampled input points position data, [B, C, S]\n", "            points1: input points data, [B, D, N]\n", "            points2: input points data, [B, D, S]\n", "        Return:\n", "            new_points: upsampled points data, [B, D', N]\n", "        \"\"\"\n", "        xyz1 = xyz1.permute(0, 2, 1)\n", "        xyz2 = xyz2.permute(0, 2, 1)\n", "        points2 = points2.permute(0, 2, 1)\n", "        B, N, C = xyz1.shape\n", "        _, S, _ = xyz2.shape\n", "\n", "        if S == 1:\n", "            interpolated_points = points2.repeat(1, N, 1)\n", "        else:\n", "            # Interpolate using inverse distance weighting\n", "            dists = square_distance(xyz1, xyz2)\n", "            dists, idx = dists.sort(dim=-1)\n", "            dists, idx = dists[:, :, :3], idx[:, :, :3]  # [B, N, 3]\n", "\n", "            dist_recip = 1.0 / (dists + 1e-8)\n", "            norm = torch.sum(dist_recip, dim=2, keepdim=True)\n", "            weight = dist_recip / norm\n", "            interpolated_points = torch.sum(index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)\n", "\n", "        if points1 is not None:\n", "            points1 = points1.permute(0, 2, 1)\n", "            new_points = torch.cat([points1, interpolated_points], dim=-1)\n", "        else:\n", "            new_points = interpolated_points\n", "\n", "        new_points = new_points.permute(0, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "        return new_points"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}