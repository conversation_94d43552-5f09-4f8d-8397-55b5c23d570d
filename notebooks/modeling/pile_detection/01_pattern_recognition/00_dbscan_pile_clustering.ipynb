{"cells": [{"cell_type": "markdown", "id": "9145b7a0", "metadata": {}, "source": ["# DBSCAN-Based Pile Clustering\n", "This notebook uses the **Density-Based Spatial Clustering of Applications with Noise (DBSCAN)** algorithm to cluster potential **pile locations** from a cleaned point cloud.\n", "\n", "This step serves as a **preprocessing stage** to:\n", "1. Reduce noise\n", "2. Group spatially connected structures\n", "3. Isolate candidate regions for downstream PointNet/ML model\n", "\n", " ---\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b26014f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 266624\n", "-rw-r--r--@ 1 <USER>  <GROUP>   130M Sep 20  2024 Croped_pointcloud_FLy4_Giorgio.las\n"]}], "source": ["!ls -lh ../../../../data/raw/piani_di_giorgio/pointcloud/"]}, {"cell_type": "code", "execution_count": 6, "id": "92d9e787", "metadata": {}, "outputs": [], "source": ["# Parameters\n", "#POINT_CLOUD_PATH = \"../../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\"\n", "\n", "POINT_CLOUD_PATH = \"../../../../data/raw/piani_di_giorgio/pointcloud/Croped_pointcloud_FLy4_Giorgio.las\"\n", "\n", "OUTPUT_DIR = \"dbscan_clusters\"\n", "\n", "# DBSCAN parameters\n", "DBSCAN_EPS = 2.5  # max distance (meters) between points in a cluster\n", "DBSCAN_MIN_SAMPLES = 8  # minimum points to form a cluster\n", "\n", "# Output options\n", "SAVE_INDIVIDUAL_CLUSTERS = True\n", "SAVE_CENTROIDS = True\n", "VISUALIZE_RESULTS = True\n"]}, {"cell_type": "code", "execution_count": 7, "id": "a7b63b0e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PILE DISCOVERY PIPELINE (NO METADATA) ===\n", "Target: Discover all pile structures on site\n", "DBSCAN eps: 2.5m, min_samples: 8\n", "Output directory: dbscan_clusters\n"]}], "source": ["print(\"=== PILE DISCOVERY PIPELINE (NO METADATA) ===\")\n", "print(f\"Target: Discover all pile structures on site\")\n", "print(f\"DBSCAN eps: {DBSCAN_EPS}m, min_samples: {DBSCAN_MIN_SAMPLES}\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "474b3528", "metadata": {}, "outputs": [], "source": ["import open3d as o3d\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import os\n", "from datetime import datetime\n", "import json\n", "import matplotlib.pyplot as plt\n", "from collections import defaultdict\n", "from sklearn.cluster import DBSCAN\n", "import warnings\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "markdown", "id": "2f4fb83a", "metadata": {}, "source": ["## Step 1: Load Point Cloud\n"]}, {"cell_type": "code", "execution_count": 9, "id": "371f5774", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul  5 21:31 \u001b[34madvanced_ifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  9 13:03 \u001b[34maligned_coordinates\u001b[m\u001b[m\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul 23 12:09 \u001b[34malignment_visualizations\u001b[m\u001b[m\n", "drwxr-xr-x@  2 <USER>  <GROUP>    64B Jul  4 17:11 \u001b[34manalysis\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 21 07:17 \u001b[34mcoordinate_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  8 <USER>  <GROUP>   256B Jul 23 14:18 \u001b[34mcorrectionnet_comparison\u001b[m\u001b[m\n", "drwxr-xr-x@  7 <USER>  <GROUP>   224B Jul 23 14:38 \u001b[34mcorrectionnet_tuning\u001b[m\u001b[m\n", "drwxr-xr-x@ 17 <USER>  <GROUP>   544B Jul 20 17:12 \u001b[34mdenoising\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul 21 12:43 \u001b[34mgcp_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul 21 13:03 \u001b[34mgcp_alignment_z_corrected\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul  4 15:07 \u001b[34mground_segmentation\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul 14 19:11 \u001b[34mifc_metadata\u001b[m\u001b[m\n", "drwxr-xr-x@ 10 <USER>  <GROUP>   320B Jul 16 22:12 \u001b[34mifc_pointclouds\u001b[m\u001b[m\n", "drwxr-xr-x@  5 <USER>  <GROUP>   160B Jul 16 13:20 \u001b[34mifc_pointclouds_corrected\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul 23 14:41 \u001b[34mlocal_nonuniform_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  8 <USER>  <GROUP>   256B Jul 23 15:15 \u001b[34mml_local_alignment\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 14 15:40 \u001b[34mvalidation\u001b[m\u001b[m\n", "drwxr-xr-x@  3 <USER>  <GROUP>    96B Jul 18 13:00 \u001b[34mz_deviation\u001b[m\u001b[m\n"]}], "source": ["!ls -lh ../../../../data/processed/trino_enel"]}, {"cell_type": "code", "execution_count": 10, "id": "79826635", "metadata": {}, "outputs": [], "source": ["import os\n", "import laspy\n", "import numpy as np\n", "import open3d as o3d\n", "\n", "def load_point_cloud(file_path):\n", "    ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    if ext == '.las':\n", "        las = laspy.read(file_path)\n", "        points = np.vstack((las.x, las.y, las.z)).T\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "    elif ext == '.ply':\n", "        pcd = o3d.io.read_point_cloud(file_path)\n", "    else:\n", "        raise ValueError(f\"Unsupported file extension: {ext}\")\n", "    \n", "    return pcd\n", "\n", "pcd = load_point_cloud(POINT_CLOUD_PATH)\n", "o3d.visualization.draw_geometries([pcd])\n"]}, {"cell_type": "markdown", "id": "0ca79b50", "metadata": {}, "source": ["## Step 2: <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 11, "id": "ddc08a8c", "metadata": {}, "outputs": [], "source": ["pcd, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "points = np.asarray(pcd.points)"]}, {"cell_type": "markdown", "id": "8f111131", "metadata": {}, "source": ["## Step 3: DBSCAN Clustering"]}, {"cell_type": "code", "execution_count": 12, "id": "3e66b2fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DBSCAN on 18017 points\n", "Found 228 clusters\n"]}], "source": ["import numpy as np\n", "from sklearn.cluster import DBSCAN\n", "import open3d as o3d\n", "\n", "# Downsample the cloud\n", "pcd_down = pcd.voxel_down_sample(voxel_size=0.5)\n", "points = np.asarray(pcd_down.points)\n", "\n", "print(f\"Running DBSCAN on {points.shape[0]} points\")\n", "\n", "# Clustering\n", "eps = 2.5\n", "min_samples = 10\n", "clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)\n", "labels = clustering.labels_\n", "n_clusters = len(set(labels)) - (1 if -1 in labels else 0)\n", "\n", "print(f\"Found {n_clusters} clusters\")\n"]}, {"cell_type": "markdown", "id": "0a16d738", "metadata": {}, "source": ["## Step 4: Visualize Clusters"]}, {"cell_type": "code", "execution_count": 13, "id": "c25da7ee", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "def visualize_clusters(points, labels):\n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "\n", "    # Colormap: RGB only, skip alpha\n", "    cmap = plt.get_cmap(\"tab20\")\n", "    colors = [\n", "        cmap(label % 20)[:3] if label != -1 else (0, 0, 0)  # strip alpha\n", "        for label in labels\n", "    ]\n", "\n", "    pcd.colors = o3d.utility.Vector3dVector(colors)\n", "    o3d.visualization.draw_geometries([pcd])\n", "\n", "\n", "from collections import Counter\n", "\n", "def filter_top_clusters(points, labels, top_k=200):\n", "    cluster_sizes = Counter(labels)\n", "    top_labels = set(\n", "        label for label, count in cluster_sizes.most_common(top_k) if label != -1\n", "    )\n", "    mask = np.isin(labels, list(top_labels))\n", "    return points[mask], labels[mask]\n", "\n", "filtered_points, filtered_labels = filter_top_clusters(points, labels, top_k=200)\n", "\n", "visualize_clusters(filtered_points, filtered_labels)\n"]}, {"cell_type": "markdown", "id": "65745ca4", "metadata": {}, "source": ["## Step 5: Save Cluster Results\n"]}, {"cell_type": "code", "execution_count": 14, "id": "b6b0b794", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 228 pile centroids\n"]}], "source": ["from collections import defaultdict\n", "\n", "clusters = defaultdict(list)\n", "\n", "# Group points by cluster label\n", "for point, label in zip(points, labels):\n", "    if label == -1:\n", "        continue\n", "    clusters[label].append(point)\n", "\n", "centroids = []\n", "for cluster_id, pts in clusters.items():\n", "    pts = np.array(pts)\n", "    center = pts.mean(axis=0)\n", "    centroids.append(center)\n", "\n", "centroids = np.array(centroids)\n", "print(f\"Extracted {len(centroids)} pile centroids\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d6b88ecb", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 15, "id": "f332313c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cluster point clouds and centroids saved.\n"]}], "source": ["import os\n", "\n", "os.makedirs(\"dbscan_clusters\", exist_ok=True)\n", "\n", "# Save each cluster as separate PCD/PLY\n", "for cluster_id, pts in clusters.items():\n", "    cluster_pcd = o3d.geometry.PointCloud()\n", "    cluster_pcd.points = o3d.utility.Vector3dVector(np.array(pts))\n", "    o3d.io.write_point_cloud(f\"dbscan_clusters/cluster_{cluster_id:03d}.ply\", cluster_pcd)\n", "\n", "# Save centroids\n", "np.savetxt(\"pile_centroids.csv\", centroids, delimiter=\",\", header=\"x,y,z\", comments=\"\")\n", "print(\"Cluster point clouds and centroids saved.\")\n"]}, {"cell_type": "code", "execution_count": 18, "id": "a8dd73c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ADVANCED PER-POINT CLUSTER LABELING TOOL ===\n", "Classes available:\n", "  0: pile\n", "  1: vegetation\n", "  2: equipment\n", "  3: pallet\n", "  4: ground\n", "  5: noise\n", "  6: unlabeled\n", "Choose labeling method:\n", "1. <PERSON><PERSON> auto-label all clusters (recommended)\n", "2. Interactive labeling session\n", "=== BATCH AUTO-LABELING 228 CLUSTERS ===\n", "Processed 51/228 clusters...\n", "Processed 101/228 clusters...\n", "Processed 151/228 clusters...\n", "Processed 201/228 clusters...\n", "\n", "============================================================\n", "BATCH AUTO-LABELING COMPLETE\n", "✅ Auto-labeled: 193 clusters\n", "⚠️  Needs review: 35 clusters\n", "❌ Errors: 0 clusters\n", "\n", "Auto-labeling breakdown:\n", "  pile: 192 clusters\n", "  pile+ground: 1 clusters\n", "\n", "⚠️  CLUSTERS NEEDING MANUAL REVIEW:\n", "  cluster_000: h=1.4m, pts=76, reason=ambiguous\n", "  cluster_002: h=1.5m, pts=89, reason=ambiguous\n", "  cluster_004: h=1.5m, pts=76, reason=ambiguous\n", "  cluster_007: h=1.5m, pts=104, reason=ambiguous\n", "  cluster_008: h=1.4m, pts=86, reason=ambiguous\n", "  cluster_015: h=1.5m, pts=70, reason=ambiguous\n", "  cluster_021: h=1.3m, pts=78, reason=ambiguous\n", "  cluster_022: h=1.3m, pts=81, reason=ambiguous\n", "  cluster_023: h=1.3m, pts=82, reason=ambiguous\n", "  cluster_027: h=1.4m, pts=84, reason=ambiguous\n", "  cluster_029: h=1.3m, pts=72, reason=ambiguous\n", "  cluster_030: h=1.3m, pts=70, reason=ambiguous\n", "  cluster_032: h=1.3m, pts=80, reason=ambiguous\n", "  cluster_033: h=1.2m, pts=77, reason=ambiguous\n", "  cluster_034: h=1.2m, pts=79, reason=ambiguous\n", "  cluster_046: h=1.5m, pts=67, reason=ambiguous\n", "  cluster_068: h=1.5m, pts=72, reason=ambiguous\n", "  cluster_071: h=1.5m, pts=72, reason=ambiguous\n", "  cluster_089: h=1.4m, pts=77, reason=ambiguous\n", "  cluster_092: h=1.4m, pts=81, reason=ambiguous\n", "  cluster_106: h=1.4m, pts=80, reason=ambiguous\n", "  cluster_116: h=1.4m, pts=94, reason=ambiguous\n", "  cluster_131: h=1.5m, pts=78, reason=ambiguous\n", "  cluster_138: h=1.4m, pts=71, reason=ambiguous\n", "  cluster_149: h=1.5m, pts=78, reason=ambiguous\n", "  cluster_152: h=1.3m, pts=82, reason=ambiguous\n", "  cluster_165: h=1.4m, pts=73, reason=ambiguous\n", "  cluster_168: h=1.3m, pts=78, reason=ambiguous\n", "  cluster_195: h=1.5m, pts=71, reason=ambiguous\n", "  cluster_203: h=1.5m, pts=78, reason=ambiguous\n", "  cluster_204: h=1.5m, pts=67, reason=ambiguous\n", "  cluster_219: h=1.5m, pts=75, reason=ambiguous\n", "  cluster_222: h=1.4m, pts=76, reason=ambiguous\n", "  cluster_225: h=1.5m, pts=70, reason=ambiguous\n", "  cluster_226: h=1.5m, pts=66, reason=ambiguous\n", "\n", "To manually label these 35 clusters, run:\n", "interactive_mixed_labeling_session()\n"]}], "source": ["# %%\n", "\"\"\"\n", "Advanced Per-Point Cluster Labeling Tool\n", "Handles mixed clusters where different regions can have different labels\n", "(e.g., piles on pallets, supports near piles, etc.)\n", "\"\"\"\n", "\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "import os\n", "import matplotlib.pyplot as plt\n", "from sklearn.neighbors import NearestNeighbors\n", "import pandas as pd\n", "\n", "# Configuration\n", "CLUSTER_DIR = Path(\"dbscan_clusters\")\n", "LABELS_DIR = Path(\"cluster_labels\")\n", "METADATA_FILE = \"labeling_metadata.json\"\n", "\n", "# Class definitions - modify these based on your needs\n", "# CLASS_MAP = {\n", "#     'pile': 0,\n", "#     'support': 1, \n", "#     'pallet': 2,\n", "#     'ground': 3,\n", "#     'noise': 4,\n", "#     'unlabeled': 5  # For points not yet labeled\n", "# }\n", "\n", "CLASS_MAP = {\n", "    'pile': 0,\n", "    'vegetation': 1,    # Add this\n", "    'equipment': 2,     # Add this  \n", "    'pallet': 3,\n", "    'ground': 4,\n", "    'noise': 5,\n", "    'unlabeled': 6  # For points not yet labeled\n", "}\n", "\n", "REVERSE_CLASS_MAP = {v: k for k, v in CLASS_MAP.items()}\n", "\n", "# Colors for visualization (RGB)\n", "CLASS_COLORS = {\n", "    0: [1.0, 0.0, 0.0],    # pile - red\n", "    1: [0.0, 1.0, 0.0],    # vegetation - green  \n", "    2: [0.0, 0.0, 1.0],    # equipment - blue\n", "    3: [0.8, 0.5, 0.2],    # pallet - brown\n", "    4: [0.5, 0.5, 0.5],    # ground - gray\n", "    5: [1.0, 1.0, 1.0],     # noise - white\n", "    6: [0.0, 0.0, 0.0]     # unlabeled - black\n", "}\n", "\n", "print(\"=== ADVANCED PER-POINT CLUSTER LABELING TOOL ===\")\n", "print(\"Classes available:\")\n", "for name, idx in CLASS_MAP.items():\n", "    print(f\"  {idx}: {name}\")\n", "\n", "# %%\n", "class ClusterLabeler:\n", "    def __init__(self, cluster_file):\n", "        self.cluster_file = Path(cluster_file)\n", "        self.cluster_name = self.cluster_file.stem\n", "        \n", "        # Load point cloud\n", "        self.pcd = o3d.io.read_point_cloud(str(cluster_file))\n", "        self.points = np.asarray(self.pcd.points)\n", "        self.num_points = len(self.points)\n", "        \n", "        # Initialize all points as unlabeled\n", "        self.point_labels = np.full(self.num_points, CLASS_MAP['unlabeled'], dtype=np.int32)\n", "        \n", "        # Load existing labels if available\n", "        self.label_file = LABELS_DIR / f\"{self.cluster_name}.npy\"\n", "        if self.label_file.exists():\n", "            self.point_labels = np.load(self.label_file)\n", "            print(f\"Loaded existing labels for {self.cluster_name}\")\n", "        \n", "        # Calculate basic stats\n", "        self.stats = self._calculate_stats()\n", "        \n", "    def _calculate_stats(self):\n", "        \"\"\"Calculate cluster statistics\"\"\"\n", "        return {\n", "            'num_points': self.num_points,\n", "            'centroid': self.points.mean(axis=0).tolist(),\n", "            'bbox_min': self.points.min(axis=0).tolist(),\n", "            'bbox_max': self.points.max(axis=0).tolist(),\n", "            'height': self.points[:, 2].max() - self.points[:, 2].min(),\n", "            'width_x': self.points[:, 0].max() - self.points[:, 0].min(),\n", "            'width_y': self.points[:, 1].max() - self.points[:, 1].min()\n", "        }\n", "    \n", "    def visualize_current_labels(self):\n", "        \"\"\"Visualize cluster with current labels\"\"\"\n", "        # Debug information\n", "        print(f\"\\nDEBUG: Visualizing {self.cluster_name}\")\n", "        print(f\"Points shape: {self.points.shape}\")\n", "        print(f\"Point range X: {self.points[:, 0].min():.2f} to {self.points[:, 0].max():.2f}\")\n", "        print(f\"Point range Y: {self.points[:, 1].min():.2f} to {self.points[:, 1].max():.2f}\")\n", "        print(f\"Point range Z: {self.points[:, 2].min():.2f} to {self.points[:, 2].max():.2f}\")\n", "        \n", "        # Create colored point cloud\n", "        vis_pcd = o3d.geometry.PointCloud()\n", "        vis_pcd.points = o3d.utility.Vector3dVector(self.points)\n", "        \n", "        # Apply colors based on labels\n", "        colors = np.array([CLASS_COLORS[label] for label in self.point_labels])\n", "        vis_pcd.colors = o3d.utility.Vector3dVector(colors)\n", "        \n", "        # Show label statistics\n", "        unique_labels, counts = np.unique(self.point_labels, return_counts=True)\n", "        print(f\"\\nCurrent labeling for {self.cluster_name}:\")\n", "        for label, count in zip(unique_labels, counts):\n", "            class_name = REVERSE_CLASS_MAP[label]\n", "            percentage = (count / self.num_points) * 100\n", "            print(f\"  {class_name}: {count} points ({percentage:.1f}%)\")\n", "        \n", "        # Enhanced visualization with better camera setup\n", "        vis = o3d.visualization.Visualizer()\n", "        vis.create_window(window_name=f\"Labels: {self.cluster_name}\", width=1200, height=800)\n", "        vis.add_geometry(vis_pcd)\n", "        \n", "        # Set better camera view\n", "        ctr = vis.get_view_control()\n", "        ctr.set_zoom(0.8)\n", "        \n", "        # Add coordinate frame for reference\n", "        coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)\n", "        coordinate_frame.translate(self.points.mean(axis=0))\n", "        vis.add_geometry(coordinate_frame)\n", "        \n", "        print(\"Visualization window opened. Close window to continue.\")\n", "        vis.run()\n", "        vis.destroy_window()\n", "    \n", "    def label_by_height_threshold(self, height_threshold, below_class, above_class):\n", "        \"\"\"Label points based on height threshold\"\"\"\n", "        z_coords = self.points[:, 2]\n", "        z_min = z_coords.min()\n", "        threshold_z = z_min + height_threshold\n", "        \n", "        below_mask = z_coords <= threshold_z\n", "        above_mask = z_coords > threshold_z\n", "        \n", "        self.point_labels[below_mask] = CLASS_MAP[below_class]\n", "        self.point_labels[above_mask] = CLASS_MAP[above_class]\n", "        \n", "        print(f\"Labeled {below_mask.sum()} points as {below_class} (below {height_threshold}m)\")\n", "        print(f\"Labeled {above_mask.sum()} points as {above_class} (above {height_threshold}m)\")\n", "    \n", "    def label_by_manual_selection(self):\n", "        \"\"\"Interactive manual point selection labeling\"\"\"\n", "        print(\"\\n=== MANUAL SELECTION MODE ===\")\n", "        print(\"This will open multiple views for manual selection\")\n", "        print(\"1. First view: Select region to label\")\n", "        print(\"2. Choose class for selected region\")\n", "        print(\"3. Repeat until satisfied\")\n", "        \n", "        current_pcd = o3d.geometry.PointCloud()\n", "        current_pcd.points = o3d.utility.Vector3dVector(self.points)\n", "        \n", "        # Apply current colors\n", "        colors = np.array([CLASS_COLORS[label] for label in self.point_labels])\n", "        current_pcd.colors = o3d.utility.Vector3dVector(colors)\n", "        \n", "        print(\"Close the window when done selecting\")\n", "        picked_points = []\n", "        \n", "        def pick_points(pcd):\n", "            vis = o3d.visualization.VisualizerWithEditing()\n", "            vis.create_window(window_name=\"Select Points to Label\")\n", "            vis.add_geometry(pcd)\n", "            vis.run()\n", "            vis.destroy_window()\n", "            return vis.get_picked_points()\n", "        \n", "        while True:\n", "            print(f\"\\nCurrent state: {np.sum(self.point_labels != CLASS_MAP['unlabeled'])} points labeled\")\n", "            print(\"Options:\")\n", "            print(\"  s: Select points to label\")\n", "            print(\"  v: View current labels\")\n", "            print(\"  d: Done with this cluster\")\n", "            \n", "            choice = input(\"Choice: \").strip().lower()\n", "            \n", "            if choice == 'd':\n", "                break\n", "            elif choice == 'v':\n", "                self.visualize_current_labels()\n", "            elif choice == 's':\n", "                # Pick points\n", "                indices = pick_points(current_pcd)\n", "                if not indices:\n", "                    print(\"No points selected\")\n", "                    continue\n", "                \n", "                print(f\"Selected {len(indices)} points\")\n", "                print(\"Assign class:\")\n", "                for name, idx in CLASS_MAP.items():\n", "                    if name != 'unlabeled':\n", "                        print(f\"  {idx}: {name}\")\n", "                \n", "                class_choice = input(\"Class number: \").strip()\n", "                if class_choice.isdigit() and int(class_choice) in REVERSE_CLASS_MAP:\n", "                    class_label = int(class_choice)\n", "                    class_name = REVERSE_CLASS_MAP[class_label]\n", "                    \n", "                    # Apply labels\n", "                    for idx in indices:\n", "                        self.point_labels[idx] = class_label\n", "                    \n", "                    print(f\"Labeled {len(indices)} points as {class_name}\")\n", "                    \n", "                    # Update colors\n", "                    colors = np.array([CLASS_COLORS[label] for label in self.point_labels])\n", "                    current_pcd.colors = o3d.utility.Vector3dVector(colors)\n", "                else:\n", "                    print(\"Invalid class number\")\n", "    \n", "    def label_by_spatial_clustering(self, method='height_layers'):\n", "        \"\"\"Automatic labeling using spatial clustering techniques\"\"\"\n", "        print(f\"\\n=== SPATIAL CLUSTERING: {method.upper()} ===\")\n", "        \n", "        if method == 'height_layers':\n", "            # Layer-based labeling (common for pile-on-pallet scenarios)\n", "            z_coords = self.points[:, 2]\n", "            z_min, z_max = z_coords.min(), z_coords.max()\n", "            height_range = z_max - z_min\n", "            \n", "            if height_range > 2.0:  # Multi-layer structure\n", "                # Bottom layer (likely pallets/ground)\n", "                bottom_threshold = z_min + 0.3  # 30cm from bottom\n", "                bottom_mask = z_coords <= bottom_threshold\n", "                \n", "                # Top layer (likely piles)  \n", "                top_threshold = z_max - 0.5  # Within 50cm of top\n", "                top_mask = z_coords >= top_threshold\n", "                \n", "                # Middle layer (supports/structure)\n", "                middle_mask = ~(bottom_mask | top_mask)\n", "                \n", "                self.point_labels[bottom_mask] = CLASS_MAP['pallet']\n", "                self.point_labels[top_mask] = CLASS_MAP['pile'] \n", "                self.point_labels[middle_mask] = CLASS_MAP['support']\n", "                \n", "                print(f\"Bottom layer (pallet): {bottom_mask.sum()} points\")\n", "                print(f\"Middle layer (support): {middle_mask.sum()} points\") \n", "                print(f\"Top layer (pile): {top_mask.sum()} points\")\n", "                \n", "        elif method == 'density_based':\n", "            # Use local point density to infer object types\n", "            from sklearn.neighbors import NearestNeighbors\n", "            \n", "            # Calculate local density\n", "            nbrs = NearestNeighbors(n_neighbors=10, algorithm='auto').fit(self.points)\n", "            distances, indices = nbrs.kneighbors(self.points)\n", "            local_density = 1.0 / (distances.mean(axis=1) + 1e-6)\n", "            \n", "            # High density = pile material, low density = structure\n", "            density_threshold = np.percentile(local_density, 70)\n", "            high_density_mask = local_density > density_threshold\n", "            \n", "            self.point_labels[high_density_mask] = CLASS_MAP['pile']\n", "            self.point_labels[~high_density_mask] = CLASS_MAP['support']\n", "            \n", "            print(f\"High density (pile): {high_density_mask.sum()} points\")\n", "            print(f\"Low density (support): {(~high_density_mask).sum()} points\")\n", "    \n", "    def save_labels(self):\n", "        \"\"\"Save the current point labels\"\"\"\n", "        LABELS_DIR.mkdir(exist_ok=True)\n", "        np.save(self.label_file, self.point_labels)\n", "        \n", "        # Create metadata\n", "        unique_labels, counts = np.unique(self.point_labels, return_counts=True)\n", "        label_distribution = {}\n", "        for label, count in zip(unique_labels, counts):\n", "            class_name = REVERSE_CLASS_MAP[label]\n", "            label_distribution[class_name] = {\n", "                'count': int(count),\n", "                'percentage': float(count / self.num_points * 100)\n", "            }\n", "        \n", "        metadata = {\n", "            'cluster_name': self.cluster_name,\n", "            'num_points': self.num_points,\n", "            'stats': self.stats,\n", "            'label_distribution': label_distribution,\n", "            'labeled_date': str(pd.Timestamp.now())\n", "        }\n", "        \n", "        return metadata\n", "\n", "# %%\n", "def interactive_mixed_labeling_session():\n", "    \"\"\"Main labeling session for mixed clusters\"\"\"\n", "    \n", "    # Load existing metadata\n", "    metadata_path = LABELS_DIR / METADATA_FILE\n", "    if metadata_path.exists():\n", "        with open(metadata_path, 'r') as f:\n", "            all_metadata = json.load(f)\n", "        print(f\"Loaded existing metadata for {len(all_metadata)} clusters\")\n", "    else:\n", "        all_metadata = {}\n", "    \n", "    # Get cluster files\n", "    cluster_files = sorted(list(CLUSTER_DIR.glob(\"*.ply\")))\n", "    if not cluster_files:\n", "        print(f\"No cluster files found in {CLUSTER_DIR}\")\n", "        return\n", "    \n", "    print(f\"Found {len(cluster_files)} cluster files\")\n", "    \n", "    current_idx = 0\n", "    while current_idx < len(cluster_files):\n", "        cluster_file = cluster_files[current_idx]\n", "        \n", "        try:\n", "            # Check if cluster file is valid\n", "            test_pcd = o3d.io.read_point_cloud(str(cluster_file))\n", "            test_points = np.asarray(test_pcd.points)\n", "            \n", "            if len(test_points) == 0:\n", "                print(f\"WARNING: {cluster_file.name} is empty, skipping...\")\n", "                current_idx += 1\n", "                continue\n", "                \n", "            # Initialize labeler\n", "            labeler = ClusterLabeler(cluster_file)\n", "            \n", "            print(f\"\\n{'='*60}\")\n", "            print(f\"Cluster {current_idx + 1}/{len(cluster_files)}: {labeler.cluster_name}\")\n", "            print(f\"Points: {labeler.num_points}, Height: {labeler.stats['height']:.2f}m\")\n", "            print(f\"Dimensions: {labeler.stats['width_x']:.1f} x {labeler.stats['width_y']:.1f} x {labeler.stats['height']:.1f}m\")\n", "            \n", "            # Add quick preview option\n", "            print(\"\\nOptions:\")\n", "            print(\"  v: View cluster (recommended first step)\")\n", "            print(\"  a: Auto-label (quick start)\")\n", "            print(\"  m: Manual labeling menu\")\n", "            print(\"  s: Skip this cluster\")\n", "            \n", "            initial_choice = input(\"Initial choice: \").strip().lower()\n", "            \n", "            if initial_choice == 's':\n", "                current_idx += 1\n", "                continue\n", "            elif initial_choice == 'v':\n", "                labeler.visualize_current_labels()\n", "            elif initial_choice == 'a':\n", "                # Quick auto-labeling for small clusters\n", "                if labeler.stats['height'] > 1.5:\n", "                    labeler.label_by_spatial_clustering('height_layers')\n", "                else:\n", "                    # Single class for small/flat clusters\n", "                    labeler.point_labels[:] = CLASS_MAP['pallet']\n", "                print(\"Auto-labeled cluster\")\n", "                labeler.visualize_current_labels()\n", "            \n", "            # Main labeling loop\n", "            while True:\n", "                print(f\"\\nDetailed labeling options for {labeler.cluster_name}:\")\n", "                print(\"  1: Height-based threshold labeling\")\n", "                print(\"  2: Spatial clustering (height layers)\")\n", "                print(\"  3: Spatial clustering (density-based)\")\n", "                print(\"  4: Manual point selection\")\n", "                print(\"  5: View current labels\")\n", "                print(\"  6: Reset all labels to unlabeled\")\n", "                print(\"  7: Quick single-class labeling\")\n", "                print(\"  s: Save and next cluster\")\n", "                print(\"  n: Next cluster (without saving)\")\n", "                print(\"  p: Previous cluster\")\n", "                print(\"  q: Quit and save all\")\n", "                \n", "                choice = input(\"Choice: \").strip().lower()\n", "                \n", "                if choice == 'q':\n", "                    # Save current cluster and exit\n", "                    cluster_metadata = labeler.save_labels()\n", "                    all_metadata[labeler.cluster_name] = cluster_metadata\n", "                    current_idx = len(cluster_files)  # Exit outer loop\n", "                    break\n", "                    \n", "                elif choice == 's':\n", "                    # Save and move to next\n", "                    cluster_metadata = labeler.save_labels()\n", "                    all_metadata[labeler.cluster_name] = cluster_metadata\n", "                    print(f\"✓ Saved labels for {labeler.cluster_name}\")\n", "                    current_idx += 1\n", "                    break\n", "                    \n", "                elif choice == 'n':\n", "                    current_idx += 1\n", "                    break\n", "                    \n", "                elif choice == 'p':\n", "                    current_idx = max(0, current_idx - 1)\n", "                    break\n", "                    \n", "                elif choice == '1':\n", "                    # Height threshold\n", "                    try:\n", "                        height = float(input(\"Height threshold (meters): \"))\n", "                        below_class = input(\"Class for points below threshold: \").strip()\n", "                        above_class = input(\"Class for points above threshold: \").strip()\n", "                        \n", "                        if below_class in CLASS_MAP and above_class in CLASS_MAP:\n", "                            labeler.label_by_height_threshold(height, below_class, above_class)\n", "                        else:\n", "                            print(\"Invalid class names\")\n", "                    except ValueError:\n", "                        print(\"Invalid height value\")\n", "                        \n", "                elif choice == '2':\n", "                    labeler.label_by_spatial_clustering('height_layers')\n", "                    \n", "                elif choice == '3':\n", "                    labeler.label_by_spatial_clustering('density_based')\n", "                    \n", "                elif choice == '4':\n", "                    labeler.label_by_manual_selection()\n", "                    \n", "                elif choice == '5':\n", "                    labeler.visualize_current_labels()\n", "                    \n", "                elif choice == '6':\n", "                    labeler.point_labels[:] = CLASS_MAP['unlabeled']\n", "                    print(\"Reset all labels to unlabeled\")\n", "                    \n", "                elif choice == '7':\n", "                    print(\"Available classes:\")\n", "                    for name, idx in CLASS_MAP.items():\n", "                        if name != 'unlabeled':\n", "                            print(f\"  {name}\")\n", "                    class_name = input(\"Class name for entire cluster: \").strip()\n", "                    if class_name in CLASS_MAP:\n", "                        labeler.point_labels[:] = CLASS_MAP[class_name]\n", "                        print(f\"Labeled entire cluster as {class_name}\")\n", "                    else:\n", "                        print(\"Invalid class name\")\n", "                    \n", "                else:\n", "                    print(\"Invalid choice\")\n", "                    \n", "        except Exception as e:\n", "            print(f\"Error processing {cluster_file.name}: {e}\")\n", "            import traceback\n", "            traceback.print_exc()\n", "            current_idx += 1\n", "            continue\n", "    \n", "    # Save final metadata\n", "    with open(metadata_path, 'w') as f:\n", "        json.dump(all_metadata, f, indent=2)\n", "    \n", "    print(f\"\\n{'='*60}\")\n", "    print(\"LABELING SESSION COMPLETE\")\n", "    print(f\"Processed clusters: {len(all_metadata)}\")\n", "    \n", "    # Summary statistics\n", "    total_points = sum(meta['num_points'] for meta in all_metadata.values())\n", "    class_totals = {}\n", "    \n", "    for meta in all_metadata.values():\n", "        for class_name, data in meta['label_distribution'].items():\n", "            if class_name not in class_totals:\n", "                class_totals[class_name] = 0\n", "            class_totals[class_name] += data['count']\n", "    \n", "    print(f\"\\nOverall label distribution ({total_points} total points):\")\n", "    for class_name, count in class_totals.items():\n", "        percentage = (count / total_points) * 100\n", "        print(f\"  {class_name}: {count:,} points ({percentage:.1f}%)\")\n", "\n", "# %%\n", "def batch_auto_label_all():\n", "    \"\"\"Automatically label all clusters with smart heuristics, flag complex ones for review\"\"\"\n", "    \n", "    LABELS_DIR.mkdir(exist_ok=True)\n", "    cluster_files = sorted(list(CLUSTER_DIR.glob(\"*.ply\")))\n", "    \n", "    if not cluster_files:\n", "        print(f\"No cluster files found in {CLUSTER_DIR}\")\n", "        return\n", "    \n", "    print(f\"=== BATCH AUTO-LABELING {len(cluster_files)} CLUSTERS ===\")\n", "    \n", "    auto_labeled = []\n", "    needs_review = []\n", "    errors = []\n", "    \n", "    for i, cluster_file in enumerate(cluster_files):\n", "        cluster_name = cluster_file.stem\n", "        \n", "        try:\n", "            # Load cluster\n", "            pcd = o3d.io.read_point_cloud(str(cluster_file))\n", "            points = np.asarray(pcd.points)\n", "            \n", "            if len(points) == 0:\n", "                print(f\"⚠️  {cluster_name}: Empty cluster - skipping\")\n", "                errors.append((cluster_name, \"empty\"))\n", "                continue\n", "            \n", "            # Calculate stats\n", "            height = points[:, 2].max() - points[:, 2].min()\n", "            num_points = len(points)\n", "            \n", "            # Smart auto-labeling for pile buffer regions\n", "            if height > 3.0 and num_points > 150:\n", "                # Large pile - use height layers\n", "                point_labels = auto_label_height_layers(points)\n", "                category = \"multi-layer\"\n", "                \n", "            elif height > 2.0 and num_points > 80:\n", "                # Medium pile - mostly pile material with some ground\n", "                point_labels = auto_label_pile_with_ground(points, height)\n", "                category = \"pile+ground\"\n", "                \n", "            elif height > 1.5 and num_points > 50:\n", "                # Small pile - likely mostly pile material\n", "                point_labels = np.full(num_points, CLASS_MAP['pile'], dtype=np.int32)\n", "                category = \"pile\"\n", "                \n", "            elif height < 0.8:\n", "                # Flat area - likely ground/equipment\n", "                point_labels = np.full(num_points, CLASS_MAP['ground'], dtype=np.int32)\n", "                category = \"ground\"\n", "                \n", "            else:\n", "                # Ambiguous case - flag for manual review\n", "                needs_review.append((cluster_name, height, num_points, \"ambiguous\"))\n", "                continue\n", "            \n", "            # Save labels\n", "            label_file = LABELS_DIR / f\"{cluster_name}.npy\"\n", "            np.save(label_file, point_labels)\n", "            \n", "            auto_labeled.append((cluster_name, category, height, num_points))\n", "            \n", "            if i % 50 == 0:\n", "                print(f\"Processed {i+1}/{len(cluster_files)} clusters...\")\n", "                \n", "        except Exception as e:\n", "            errors.append((cluster_name, str(e)))\n", "            continue\n", "    \n", "    # Save metadata\n", "    metadata = {}\n", "    for cluster_name, category, height, num_points in auto_labeled:\n", "        unique_labels, counts = np.unique(np.load(LABELS_DIR / f\"{cluster_name}.npy\"), return_counts=True)\n", "        label_dist = {}\n", "        for label, count in zip(unique_labels, counts):\n", "            class_name = REVERSE_CLASS_MAP[label]\n", "            label_dist[class_name] = {'count': int(count), 'percentage': float(count/num_points*100)}\n", "        \n", "        metadata[cluster_name] = {\n", "            'auto_labeled': True,\n", "            'category': category,\n", "            'num_points': num_points,\n", "            'height': height,\n", "            'label_distribution': label_dist,\n", "            'labeled_date': str(pd.Timestamp.now())\n", "        }\n", "    \n", "    metadata_path = LABELS_DIR / METADATA_FILE\n", "    with open(metadata_path, 'w') as f:\n", "        json.dump(metadata, f, indent=2)\n", "    \n", "    # Print summary\n", "    print(f\"\\n{'='*60}\")\n", "    print(\"BATCH AUTO-LABELING COMPLETE\")\n", "    print(f\"✅ Auto-labeled: {len(auto_labeled)} clusters\")\n", "    print(f\"⚠️  Needs review: {len(needs_review)} clusters\") \n", "    print(f\"❌ Errors: {len(errors)} clusters\")\n", "    \n", "    # Show auto-labeling breakdown\n", "    categories = {}\n", "    for _, category, _, _ in auto_labeled:\n", "        categories[category] = categories.get(category, 0) + 1\n", "    \n", "    print(f\"\\nAuto-labeling breakdown:\")\n", "    for category, count in categories.items():\n", "        print(f\"  {category}: {count} clusters\")\n", "    \n", "    # Show clusters that need manual review\n", "    if needs_review:\n", "        print(f\"\\n⚠️  CLUSTERS NEEDING MANUAL REVIEW:\")\n", "        for cluster_name, height, num_points, reason in needs_review:\n", "            print(f\"  {cluster_name}: h={height:.1f}m, pts={num_points}, reason={reason}\")\n", "        \n", "        print(f\"\\nTo manually label these {len(needs_review)} clusters, run:\")\n", "        print(\"interactive_mixed_labeling_session()\")\n", "    \n", "    if errors:\n", "        print(f\"\\n❌ CLUSTERS WITH ERRORS:\")\n", "        for cluster_name, error in errors:\n", "            print(f\"  {cluster_name}: {error}\")\n", "    \n", "    return auto_labeled, needs_review, errors\n", "\n", "def auto_label_height_layers(points):\n", "    \"\"\"Auto-label using height layers for complex piles\"\"\"\n", "    z_coords = points[:, 2]\n", "    z_min, z_max = z_coords.min(), z_coords.max()\n", "    height_range = z_max - z_min\n", "    \n", "    # Bottom 20% = ground\n", "    bottom_threshold = z_min + 0.2 * height_range\n", "    # Top 60% = pile material  \n", "    top_threshold = z_min + 0.4 * height_range\n", "    \n", "    point_labels = np.full(len(points), CLASS_MAP['support'], dtype=np.int32)  # default middle\n", "    point_labels[z_coords <= bottom_threshold] = CLASS_MAP['ground']\n", "    point_labels[z_coords >= top_threshold] = CLASS_MAP['pile']\n", "    \n", "    return point_labels\n", "\n", "def auto_label_pile_with_ground(points, height):\n", "    \"\"\"Auto-label pile material with ground base\"\"\"\n", "    z_coords = points[:, 2]\n", "    z_min = z_coords.min()\n", "    \n", "    # Bottom 30cm = ground, rest = pile\n", "    ground_threshold = z_min + min(0.3, height * 0.2)\n", "    \n", "    point_labels = np.full(len(points), CLASS_MAP['pile'], dtype=np.int32)\n", "    point_labels[z_coords <= ground_threshold] = CLASS_MAP['ground']\n", "    \n", "    return point_labels\n", "\n", "if __name__ == \"__main__\":\n", "    print(\"Choose labeling method:\")\n", "    print(\"1. <PERSON><PERSON> auto-label all clusters (recommended)\")\n", "    print(\"2. Interactive labeling session\")\n", "    \n", "    choice = input(\"Enter choice (1 or 2): \").strip()\n", "    \n", "    if choice == \"1\":\n", "        batch_auto_label_all()\n", "    elif choice == \"2\":\n", "        interactive_mixed_labeling_session()\n", "    else:\n", "        print(\"Invalid choice\")"]}, {"cell_type": "code", "execution_count": 19, "id": "d2530f3b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CLUSTER DATA EXPORTER ===\n", "Export cluster data for use in external visualization tools\n", "Choose export format:\n", "1. CSV summary (for Excel, Google Sheets)\n", "2. Pile inventory list\n", "3. Detailed coordinates for specific clusters\n", "4. All of the above\n", "\n", "Exporting CSV summary...\n", "Exported cluster summary to: cluster_exports/cluster_summary.csv\n", "Contains 193 clusters\n", "\n", "Summary Statistics:\n", "Total points across all clusters: 15,307\n", "Average height: 1.68m\n", "Height range: 1.50m to 2.15m\n", "\n", "By category:\n", "  pile: 192 clusters (avg height: 1.68m)\n", "  pile+ground: 1 clusters (avg height: 2.15m)\n", "\n", "Clusters with mixed/non-pile content:\n", "  cluster_111: 40.0% pile\n", "\n", "All exports saved to: cluster_exports/\n", "You can now use these files in Excel, GIS software, or other visualization tools!\n"]}], "source": ["# %%\n", "\"\"\"\n", "Cluster Data Exporter\n", "Export cluster information and coordinates for use in external tools\n", "\"\"\"\n", "\n", "import numpy as np\n", "import open3d as o3d\n", "import pandas as pd\n", "from pathlib import Path\n", "import json\n", "import csv\n", "\n", "# Configuration\n", "CLUSTER_DIR = Path(\"dbscan_clusters\")\n", "LABELS_DIR = Path(\"cluster_labels\")\n", "METADATA_FILE = \"labeling_metadata.json\"\n", "OUTPUT_DIR = Path(\"cluster_exports\")\n", "\n", "CLASS_NAMES = {0: 'pile', 1: 'support', 2: 'pallet', 3: 'ground', 4: 'noise', 5: 'unlabeled'}\n", "\n", "def load_cluster_data(cluster_name):\n", "    \"\"\"Load cluster points and labels\"\"\"\n", "    cluster_file = CLUSTER_DIR / f\"{cluster_name}.ply\"\n", "    pcd = o3d.io.read_point_cloud(str(cluster_file))\n", "    points = np.asarray(pcd.points)\n", "    \n", "    label_file = LABELS_DIR / f\"{cluster_name}.npy\"\n", "    labels = np.load(label_file) if label_file.exists() else None\n", "    \n", "    return points, labels\n", "\n", "def export_cluster_summary():\n", "    \"\"\"Export summary table of all clusters\"\"\"\n", "    metadata_path = LABELS_DIR / METADATA_FILE\n", "    if not metadata_path.exists():\n", "        print(\"No metadata found\")\n", "        return\n", "    \n", "    with open(metadata_path, 'r') as f:\n", "        all_metadata = json.load(f)\n", "    \n", "    # Create summary data\n", "    summary_data = []\n", "    \n", "    for cluster_name, meta in all_metadata.items():\n", "        row = {\n", "            'cluster_name': cluster_name,\n", "            'num_points': meta.get('num_points', 0),\n", "            'height_m': round(meta.get('height', 0), 2),\n", "            'category': meta.get('category', 'unknown'),\n", "            'auto_labeled': meta.get('auto_labeled', False)\n", "        }\n", "        \n", "        # Add label distribution\n", "        label_dist = meta.get('label_distribution', {})\n", "        for class_name in ['pile', 'ground', 'support', 'pallet', 'noise']:\n", "            if class_name in label_dist:\n", "                row[f'{class_name}_points'] = label_dist[class_name]['count']\n", "                row[f'{class_name}_percent'] = round(label_dist[class_name]['percentage'], 1)\n", "            else:\n", "                row[f'{class_name}_points'] = 0\n", "                row[f'{class_name}_percent'] = 0.0\n", "        \n", "        summary_data.append(row)\n", "    \n", "    # Convert to DataFrame and sort\n", "    df = pd.DataFrame(summary_data)\n", "    df = df.sort_values('cluster_name')\n", "    \n", "    # Export to CSV\n", "    OUTPUT_DIR.mkdir(exist_ok=True)\n", "    summary_file = OUTPUT_DIR / \"cluster_summary.csv\"\n", "    df.to_csv(summary_file, index=False)\n", "    \n", "    print(f\"Exported cluster summary to: {summary_file}\")\n", "    print(f\"Contains {len(df)} clusters\")\n", "    \n", "    # Show some statistics\n", "    print(f\"\\nSummary Statistics:\")\n", "    print(f\"Total points across all clusters: {df['num_points'].sum():,}\")\n", "    print(f\"Average height: {df['height_m'].mean():.2f}m\")\n", "    print(f\"Height range: {df['height_m'].min():.2f}m to {df['height_m'].max():.2f}m\")\n", "    \n", "    # Category breakdown\n", "    print(f\"\\nBy category:\")\n", "    category_counts = df['category'].value_counts()\n", "    for category, count in category_counts.items():\n", "        avg_height = df[df['category'] == category]['height_m'].mean()\n", "        print(f\"  {category}: {count} clusters (avg height: {avg_height:.2f}m)\")\n", "    \n", "    # Show clusters that are NOT pure pile\n", "    non_pile = df[df['pile_percent'] < 100]\n", "    if len(non_pile) > 0:\n", "        print(f\"\\nClusters with mixed/non-pile content:\")\n", "        for _, row in non_pile.iterrows():\n", "            print(f\"  {row['cluster_name']}: {row['pile_percent']:.1f}% pile\")\n", "    \n", "    return df\n", "\n", "def export_specific_clusters(cluster_names, include_coordinates=True):\n", "    \"\"\"Export detailed data for specific clusters\"\"\"\n", "    OUTPUT_DIR.mkdir(exist_ok=True)\n", "    \n", "    for cluster_name in cluster_names:\n", "        print(f\"\\nExporting {cluster_name}...\")\n", "        \n", "        try:\n", "            points, labels = load_cluster_data(cluster_name)\n", "            \n", "            if include_coordinates:\n", "                # Create detailed CSV with coordinates and labels\n", "                data = {\n", "                    'x': points[:, 0],\n", "                    'y': points[:, 1], \n", "                    'z': points[:, 2],\n", "                    'height_above_min': points[:, 2] - points[:, 2].min()\n", "                }\n", "                \n", "                if labels is not None:\n", "                    data['label_id'] = labels\n", "                    data['label_name'] = [CLASS_NAMES.get(label, f'class_{label}') for label in labels]\n", "                \n", "                df = pd.DataFrame(data)\n", "                \n", "                # Export coordinates CSV\n", "                coord_file = OUTPUT_DIR / f\"{cluster_name}_coordinates.csv\"\n", "                df.to_csv(coord_file, index=False)\n", "                print(f\"  Coordinates saved to: {coord_file}\")\n", "                \n", "            # Create summary text file\n", "            summary_file = OUTPUT_DIR / f\"{cluster_name}_summary.txt\"\n", "            with open(summary_file, 'w') as f:\n", "                f.write(f\"CLUSTER ANALYSIS: {cluster_name}\\n\")\n", "                f.write(\"=\"*50 + \"\\n\\n\")\n", "                \n", "                f.write(f\"Basic Information:\\n\")\n", "                f.write(f\"  Total points: {len(points):,}\\n\")\n", "                f.write(f\"  X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f} ({points[:, 0].max()-points[:, 0].min():.2f}m span)\\n\")\n", "                f.write(f\"  Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f} ({points[:, 1].max()-points[:, 1].min():.2f}m span)\\n\")\n", "                f.write(f\"  Z range: {points[:, 2].min():.2f} to {points[:, 2].max():.2f} ({points[:, 2].max()-points[:, 2].min():.2f}m height)\\n\\n\")\n", "                \n", "                if labels is not None:\n", "                    f.write(f\"Label Distribution:\\n\")\n", "                    unique_labels, counts = np.unique(labels, return_counts=True)\n", "                    for label, count in zip(unique_labels, counts):\n", "                        class_name = CLASS_NAMES.get(label, f'class_{label}')\n", "                        pct = (count / len(labels)) * 100\n", "                        f.write(f\"  {class_name}: {count:,} points ({pct:.1f}%)\\n\")\n", "                    \n", "                    f.write(f\"\\nSpatial Analysis by Label:\\n\")\n", "                    for label in unique_labels:\n", "                        mask = labels == label\n", "                        if np.sum(mask) > 0:\n", "                            label_points = points[mask]\n", "                            class_name = CLASS_NAMES.get(label, f'class_{label}')\n", "                            f.write(f\"  {class_name}:\\n\")\n", "                            f.write(f\"    Points: {np.sum(mask):,}\\n\")\n", "                            f.write(f\"    Height range: {label_points[:, 2].min():.2f} to {label_points[:, 2].max():.2f}m\\n\")\n", "                            f.write(f\"    Average height: {label_points[:, 2].mean():.2f}m\\n\")\n", "                            f.write(f\"    Height std: {label_points[:, 2].std():.2f}m\\n\")\n", "                \n", "            print(f\"  Summary saved to: {summary_file}\")\n", "            \n", "        except Exception as e:\n", "            print(f\"  Error exporting {cluster_name}: {e}\")\n", "\n", "def list_pile_numbers():\n", "    \"\"\"List all pile locations with their properties\"\"\"\n", "    metadata_path = LABELS_DIR / METADATA_FILE\n", "    if not metadata_path.exists():\n", "        print(\"No metadata found\")\n", "        return\n", "    \n", "    with open(metadata_path, 'r') as f:\n", "        all_metadata = json.load(f)\n", "    \n", "    print(f\"PILE INVENTORY\")\n", "    print(\"=\"*60)\n", "    \n", "    pile_data = []\n", "    for cluster_name, meta in all_metadata.items():\n", "        # Extract pile number from cluster name\n", "        pile_num = cluster_name.replace('cluster_', '')\n", "        \n", "        pile_info = {\n", "            'pile_number': pile_num,\n", "            'total_points': meta.get('num_points', 0),\n", "            'height_m': round(meta.get('height', 0), 2),\n", "            'category': meta.get('category', 'unknown')\n", "        }\n", "        \n", "        # Get pile content percentage\n", "        label_dist = meta.get('label_distribution', {})\n", "        pile_pct = label_dist.get('pile', {}).get('percentage', 0)\n", "        pile_points = label_dist.get('pile', {}).get('count', 0)\n", "        \n", "        pile_info['pile_percentage'] = round(pile_pct, 1)\n", "        pile_info['pile_points'] = pile_points\n", "        \n", "        # Determine pile status\n", "        if pile_pct >= 90:\n", "            status = \"Pure pile\"\n", "        elif pile_pct >= 50:\n", "            status = \"Mostly pile\"\n", "        elif pile_pct > 0:\n", "            status = \"Some pile\"\n", "        else:\n", "            status = \"No pile\"\n", "        \n", "        pile_info['status'] = status\n", "        pile_data.append(pile_info)\n", "    \n", "    # Sort by pile number\n", "    pile_data.sort(key=lambda x: int(x['pile_number']))\n", "    \n", "    # Print table\n", "    print(f\"{'Pile#':<6} {'Height':<8} {'Points':<8} {'Pile%':<8} {'Status':<12} {'Category':<15}\")\n", "    print(\"-\" * 65)\n", "    \n", "    for pile in pile_data:\n", "        print(f\"{pile['pile_number']:<6} {pile['height_m']:<8} {pile['total_points']:<8} {pile['pile_percentage']:<8} {pile['status']:<12} {pile['category']:<15}\")\n", "    \n", "    # Summary stats\n", "    print(f\"\\nSUMMARY:\")\n", "    print(f\"Total pile locations: {len(pile_data)}\")\n", "    \n", "    status_counts = {}\n", "    for pile in pile_data:\n", "        status = pile['status']\n", "        status_counts[status] = status_counts.get(status, 0) + 1\n", "    \n", "    for status, count in status_counts.items():\n", "        print(f\"  {status}: {count} locations\")\n", "    \n", "    # Save to file\n", "    OUTPUT_DIR.mkdir(exist_ok=True)\n", "    pile_list_file = OUTPUT_DIR / \"pile_inventory.txt\"\n", "    with open(pile_list_file, 'w') as f:\n", "        f.write(\"PILE INVENTORY\\n\")\n", "        f.write(\"=\"*60 + \"\\n\\n\")\n", "        f.write(f\"{'Pile#':<6} {'Height':<8} {'Points':<8} {'Pile%':<8} {'Status':<12} {'Category':<15}\\n\")\n", "        f.write(\"-\" * 65 + \"\\n\")\n", "        for pile in pile_data:\n", "            f.write(f\"{pile['pile_number']:<6} {pile['height_m']:<8} {pile['total_points']:<8} {pile['pile_percentage']:<8} {pile['status']:<12} {pile['category']:<15}\\n\")\n", "    \n", "    print(f\"\\nPile inventory saved to: {pile_list_file}\")\n", "    \n", "    return pile_data\n", "\n", "def export_for_external_tools():\n", "    \"\"\"Export data in formats suitable for external visualization tools\"\"\"\n", "    OUTPUT_DIR.mkdir(exist_ok=True)\n", "    \n", "    print(\"Choose export format:\")\n", "    print(\"1. CSV summary (for Excel, Google Sheets)\")\n", "    print(\"2. <PERSON>le inventory list\")\n", "    print(\"3. Detailed coordinates for specific clusters\")\n", "    print(\"4. All of the above\")\n", "    \n", "    choice = input(\"Choice (1-4): \").strip()\n", "    \n", "    if choice in ['1', '4']:\n", "        print(\"\\nExporting CSV summary...\")\n", "        export_cluster_summary()\n", "    \n", "    if choice in ['2', '4']:\n", "        print(\"\\nGenerating pile inventory...\")\n", "        list_pile_numbers()\n", "    \n", "    if choice in ['3', '4']:\n", "        print(\"\\nWhich clusters do you want detailed coordinates for?\")\n", "        print(\"Examples: cluster_000,cluster_111,cluster_045\")\n", "        print(\"Or enter 'mixed' for clusters with multiple labels\")\n", "        print(\"Or enter 'sample' for 5 random clusters\")\n", "        \n", "        cluster_input = input(\"Clusters: \").strip()\n", "        \n", "        if cluster_input.lower() == 'mixed':\n", "            # Find mixed clusters\n", "            metadata_path = LABELS_DIR / METADATA_FILE\n", "            with open(metadata_path, 'r') as f:\n", "                all_metadata = json.load(f)\n", "            \n", "            mixed_clusters = []\n", "            for name, meta in all_metadata.items():\n", "                label_dist = meta.get('label_distribution', {})\n", "                if len(label_dist) > 1:\n", "                    mixed_clusters.append(name)\n", "            \n", "            if mixed_clusters:\n", "                print(f\"Found {len(mixed_clusters)} mixed clusters: {mixed_clusters}\")\n", "                export_specific_clusters(mixed_clusters)\n", "            else:\n", "                print(\"No mixed clusters found\")\n", "                \n", "        elif cluster_input.lower() == 'sample':\n", "            metadata_path = LABELS_DIR / METADATA_FILE\n", "            with open(metadata_path, 'r') as f:\n", "                all_metadata = json.load(f)\n", "            \n", "            sample_clusters = np.random.choice(list(all_metadata.keys()), 5, replace=False)\n", "            print(f\"Sampling: {list(sample_clusters)}\")\n", "            export_specific_clusters(sample_clusters)\n", "            \n", "        else:\n", "            cluster_names = [name.strip() for name in cluster_input.split(',')]\n", "            export_specific_clusters(cluster_names)\n", "    \n", "    print(f\"\\nAll exports saved to: {OUTPUT_DIR}/\")\n", "    print(\"You can now use these files in Excel, GIS software, or other visualization tools!\")\n", "\n", "# %%\n", "if __name__ == \"__main__\":\n", "    print(\"=== CLUSTER DATA EXPORTER ===\")\n", "    print(\"Export cluster data for use in external visualization tools\")\n", "    \n", "    export_for_external_tools()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}