{"cells": [{"cell_type": "markdown", "id": "9145b7a0", "metadata": {}, "source": ["# DBSCAN-Based Pile Clustering\n", "This notebook uses the **Density-Based Spatial Clustering of Applications with Noise (DBSCAN)** algorithm to cluster potential **pile locations** from a cleaned point cloud.\n", "\n", "This step serves as a **preprocessing stage** to:\n", "1. Reduce noise\n", "2. Group spatially connected structures\n", "3. Isolate candidate regions for downstream PointNet/ML model\n", "\n", " ---\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis\n"]}, {"cell_type": "code", "execution_count": null, "id": "b26014f2", "metadata": {}, "outputs": [], "source": ["!ls -lh ../../../../data/raw/piani_di_giorgio/pointcloud/"]}, {"cell_type": "code", "execution_count": 2, "id": "92d9e787", "metadata": {}, "outputs": [], "source": ["# Parameters\n", "#POINT_CLOUD_PATH = \"../../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\"\n", "\n", "POINT_CLOUD_PATH = \"../../../../data/raw/piani_di_giorgio/pointcloud/Croped_pointcloud_FLy4_Giorgio.las\"\n", "\n", "OUTPUT_DIR = \"dbscan_clusters\"\n", "\n", "# DBSCAN parameters\n", "DBSCAN_EPS = 2.5  # max distance (meters) between points in a cluster\n", "DBSCAN_MIN_SAMPLES = 8  # minimum points to form a cluster\n", "\n", "# Output options\n", "SAVE_INDIVIDUAL_CLUSTERS = True\n", "SAVE_CENTROIDS = True\n", "VISUALIZE_RESULTS = True\n"]}, {"cell_type": "code", "execution_count": null, "id": "a7b63b0e", "metadata": {}, "outputs": [], "source": ["print(\"=== PILE DISCOVERY PIPELINE (NO METADATA) ===\")\n", "print(f\"Target: Discover all pile structures on site\")\n", "print(f\"DBSCAN eps: {DBSCAN_EPS}m, min_samples: {DBSCAN_MIN_SAMPLES}\")\n", "print(f\"Output directory: {OUTPUT_DIR}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "474b3528", "metadata": {}, "outputs": [], "source": ["import open3d as o3d\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "import os\n", "from datetime import datetime\n", "import json\n", "import matplotlib.pyplot as plt\n", "from collections import defaultdict\n", "from sklearn.cluster import DBSCAN\n", "import warnings\n", "warnings.filterwarnings('ignore')\n"]}, {"cell_type": "markdown", "id": "2f4fb83a", "metadata": {}, "source": ["## Step 1: Load Point Cloud\n"]}, {"cell_type": "code", "execution_count": null, "id": "371f5774", "metadata": {}, "outputs": [], "source": ["!ls -lh ../../../../data/processed/trino_enel"]}, {"cell_type": "code", "execution_count": 10, "id": "79826635", "metadata": {}, "outputs": [], "source": ["import os\n", "import laspy\n", "import numpy as np\n", "import open3d as o3d\n", "\n", "def load_point_cloud(file_path):\n", "    ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    if ext == '.las':\n", "        las = laspy.read(file_path)\n", "        points = np.vstack((las.x, las.y, las.z)).T\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "    elif ext == '.ply':\n", "        pcd = o3d.io.read_point_cloud(file_path)\n", "    else:\n", "        raise ValueError(f\"Unsupported file extension: {ext}\")\n", "    \n", "    return pcd\n", "\n", "pcd = load_point_cloud(POINT_CLOUD_PATH)\n", "o3d.visualization.draw_geometries([pcd])\n"]}, {"cell_type": "markdown", "id": "0ca79b50", "metadata": {}, "source": ["## Step 2: <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 5, "id": "ddc08a8c", "metadata": {}, "outputs": [], "source": ["pcd, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)\n", "points = np.asarray(pcd.points)"]}, {"cell_type": "markdown", "id": "8f111131", "metadata": {}, "source": ["## Step 3: DBSCAN Clustering"]}, {"cell_type": "code", "execution_count": 12, "id": "3e66b2fb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running DBSCAN on 18444 points\n", "Found 228 clusters\n"]}], "source": ["import numpy as np\n", "from sklearn.cluster import DBSCAN\n", "import open3d as o3d\n", "\n", "# Downsample the cloud\n", "pcd_down = pcd.voxel_down_sample(voxel_size=0.5)\n", "points = np.asarray(pcd_down.points)\n", "\n", "print(f\"Running DBSCAN on {points.shape[0]} points\")\n", "\n", "# Clustering\n", "eps = 2.5\n", "min_samples = 10\n", "clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)\n", "labels = clustering.labels_\n", "n_clusters = len(set(labels)) - (1 if -1 in labels else 0)\n", "\n", "print(f\"Found {n_clusters} clusters\")\n"]}, {"cell_type": "markdown", "id": "0a16d738", "metadata": {}, "source": ["## Step 4: Visualize Clusters"]}, {"cell_type": "code", "execution_count": 7, "id": "c25da7ee", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import numpy as np\n", "\n", "def visualize_clusters(points, labels):\n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(points)\n", "\n", "    # Colormap: RGB only, skip alpha\n", "    cmap = plt.get_cmap(\"tab20\")\n", "    colors = [\n", "        cmap(label % 20)[:3] if label != -1 else (0, 0, 0)  # strip alpha\n", "        for label in labels\n", "    ]\n", "\n", "    pcd.colors = o3d.utility.Vector3dVector(colors)\n", "    o3d.visualization.draw_geometries([pcd])\n", "\n", "\n", "from collections import Counter\n", "\n", "def filter_top_clusters(points, labels, top_k=200):\n", "    cluster_sizes = Counter(labels)\n", "    top_labels = set(\n", "        label for label, count in cluster_sizes.most_common(top_k) if label != -1\n", "    )\n", "    mask = np.isin(labels, list(top_labels))\n", "    return points[mask], labels[mask]\n", "\n", "filtered_points, filtered_labels = filter_top_clusters(points, labels, top_k=200)\n", "\n", "visualize_clusters(filtered_points, filtered_labels)\n"]}, {"cell_type": "markdown", "id": "65745ca4", "metadata": {}, "source": ["## Step 5: Save Cluster Results\n"]}, {"cell_type": "code", "execution_count": 8, "id": "b6b0b794", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracted 228 pile centroids\n"]}], "source": ["from collections import defaultdict\n", "\n", "clusters = defaultdict(list)\n", "\n", "# Group points by cluster label\n", "for point, label in zip(points, labels):\n", "    if label == -1:\n", "        continue\n", "    clusters[label].append(point)\n", "\n", "centroids = []\n", "for cluster_id, pts in clusters.items():\n", "    pts = np.array(pts)\n", "    center = pts.mean(axis=0)\n", "    centroids.append(center)\n", "\n", "centroids = np.array(centroids)\n", "print(f\"Extracted {len(centroids)} pile centroids\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "f332313c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cluster point clouds and centroids saved.\n"]}], "source": ["import os\n", "\n", "os.makedirs(\"dbscan_clusters\", exist_ok=True)\n", "\n", "# Save each cluster as separate PCD/PLY\n", "for cluster_id, pts in clusters.items():\n", "    cluster_pcd = o3d.geometry.PointCloud()\n", "    cluster_pcd.points = o3d.utility.Vector3dVector(np.array(pts))\n", "    o3d.io.write_point_cloud(f\"dbscan_clusters/cluster_{cluster_id:03d}.ply\", cluster_pcd)\n", "\n", "# Save centroids\n", "np.savetxt(\"pile_centroids.csv\", centroids, delimiter=\",\", header=\"x,y,z\", comments=\"\")\n", "print(\"Cluster point clouds and centroids saved.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}