# %%
"""
Advanced Per-Point Cluster Labeling Tool
Handles mixed clusters where different regions can have different labels
(e.g., piles on pallets, supports near piles, etc.)
"""

import numpy as np
import open3d as o3d
from pathlib import Path
import json
import os
import matplotlib.pyplot as plt
from sklearn.neighbors import NearestNeighbors
import pandas as pd

# Configuration
CLUSTER_DIR = Path("dbscan_clusters")
LABELS_DIR = Path("cluster_labels")
METADATA_FILE = "labeling_metadata.json"

# Class definitions - modify these based on your needs
# CLASS_MAP = {
#     'pile': 0,
#     'support': 1, 
#     'pallet': 2,
#     'ground': 3,
#     'noise': 4,
#     'unlabeled': 5  # For points not yet labeled
# }

CLASS_MAP = {
    'pile': 0,
    'vegetation': 1,    # Add this
    'equipment': 2,     # Add this  
    'pallet': 3,
    'ground': 4,
    'noise': 5,
    'unlabeled': 6  # For points not yet labeled
}

REVERSE_CLASS_MAP = {v: k for k, v in CLASS_MAP.items()}

# Colors for visualization (RGB)
CLASS_COLORS = {
    0: [1.0, 0.0, 0.0],    # pile - red
    1: [0.0, 1.0, 0.0],    # vegetation - green  
    2: [0.0, 0.0, 1.0],    # equipment - blue
    3: [0.8, 0.5, 0.2],    # pallet - brown
    4: [0.5, 0.5, 0.5],    # ground - gray
    5: [1.0, 1.0, 1.0],     # noise - white
    6: [0.0, 0.0, 0.0]     # unlabeled - black
}

print("=== ADVANCED PER-POINT CLUSTER LABELING TOOL ===")
print("Classes available:")
for name, idx in CLASS_MAP.items():
    print(f"  {idx}: {name}")

# %%
class ClusterLabeler:
    def __init__(self, cluster_file):
        self.cluster_file = Path(cluster_file)
        self.cluster_name = self.cluster_file.stem
        
        # Load point cloud
        self.pcd = o3d.io.read_point_cloud(str(cluster_file))
        self.points = np.asarray(self.pcd.points)
        self.num_points = len(self.points)
        
        # Initialize all points as unlabeled
        self.point_labels = np.full(self.num_points, CLASS_MAP['unlabeled'], dtype=np.int32)
        
        # Load existing labels if available
        self.label_file = LABELS_DIR / f"{self.cluster_name}.npy"
        if self.label_file.exists():
            self.point_labels = np.load(self.label_file)
            print(f"Loaded existing labels for {self.cluster_name}")
        
        # Calculate basic stats
        self.stats = self._calculate_stats()
        
    def _calculate_stats(self):
        """Calculate cluster statistics"""
        return {
            'num_points': self.num_points,
            'centroid': self.points.mean(axis=0).tolist(),
            'bbox_min': self.points.min(axis=0).tolist(),
            'bbox_max': self.points.max(axis=0).tolist(),
            'height': self.points[:, 2].max() - self.points[:, 2].min(),
            'width_x': self.points[:, 0].max() - self.points[:, 0].min(),
            'width_y': self.points[:, 1].max() - self.points[:, 1].min()
        }
    
    def visualize_current_labels(self):
        """Visualize cluster with current labels"""
        # Debug information
        print(f"\nDEBUG: Visualizing {self.cluster_name}")
        print(f"Points shape: {self.points.shape}")
        print(f"Point range X: {self.points[:, 0].min():.2f} to {self.points[:, 0].max():.2f}")
        print(f"Point range Y: {self.points[:, 1].min():.2f} to {self.points[:, 1].max():.2f}")
        print(f"Point range Z: {self.points[:, 2].min():.2f} to {self.points[:, 2].max():.2f}")
        
        # Create colored point cloud
        vis_pcd = o3d.geometry.PointCloud()
        vis_pcd.points = o3d.utility.Vector3dVector(self.points)
        
        # Apply colors based on labels
        colors = np.array([CLASS_COLORS[label] for label in self.point_labels])
        vis_pcd.colors = o3d.utility.Vector3dVector(colors)
        
        # Show label statistics
        unique_labels, counts = np.unique(self.point_labels, return_counts=True)
        print(f"\nCurrent labeling for {self.cluster_name}:")
        for label, count in zip(unique_labels, counts):
            class_name = REVERSE_CLASS_MAP[label]
            percentage = (count / self.num_points) * 100
            print(f"  {class_name}: {count} points ({percentage:.1f}%)")
        
        # Enhanced visualization with better camera setup
        vis = o3d.visualization.Visualizer()
        vis.create_window(window_name=f"Labels: {self.cluster_name}", width=1200, height=800)
        vis.add_geometry(vis_pcd)
        
        # Set better camera view
        ctr = vis.get_view_control()
        ctr.set_zoom(0.8)
        
        # Add coordinate frame for reference
        coordinate_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
        coordinate_frame.translate(self.points.mean(axis=0))
        vis.add_geometry(coordinate_frame)
        
        print("Visualization window opened. Close window to continue.")
        vis.run()
        vis.destroy_window()
    
    def label_by_height_threshold(self, height_threshold, below_class, above_class):
        """Label points based on height threshold"""
        z_coords = self.points[:, 2]
        z_min = z_coords.min()
        threshold_z = z_min + height_threshold
        
        below_mask = z_coords <= threshold_z
        above_mask = z_coords > threshold_z
        
        self.point_labels[below_mask] = CLASS_MAP[below_class]
        self.point_labels[above_mask] = CLASS_MAP[above_class]
        
        print(f"Labeled {below_mask.sum()} points as {below_class} (below {height_threshold}m)")
        print(f"Labeled {above_mask.sum()} points as {above_class} (above {height_threshold}m)")
    
    def label_by_manual_selection(self):
        """Interactive manual point selection labeling"""
        print("\n=== MANUAL SELECTION MODE ===")
        print("This will open multiple views for manual selection")
        print("1. First view: Select region to label")
        print("2. Choose class for selected region")
        print("3. Repeat until satisfied")
        
        current_pcd = o3d.geometry.PointCloud()
        current_pcd.points = o3d.utility.Vector3dVector(self.points)
        
        # Apply current colors
        colors = np.array([CLASS_COLORS[label] for label in self.point_labels])
        current_pcd.colors = o3d.utility.Vector3dVector(colors)
        
        print("Close the window when done selecting")
        picked_points = []
        
        def pick_points(pcd):
            vis = o3d.visualization.VisualizerWithEditing()
            vis.create_window(window_name="Select Points to Label")
            vis.add_geometry(pcd)
            vis.run()
            vis.destroy_window()
            return vis.get_picked_points()
        
        while True:
            print(f"\nCurrent state: {np.sum(self.point_labels != CLASS_MAP['unlabeled'])} points labeled")
            print("Options:")
            print("  s: Select points to label")
            print("  v: View current labels")
            print("  d: Done with this cluster")
            
            choice = input("Choice: ").strip().lower()
            
            if choice == 'd':
                break
            elif choice == 'v':
                self.visualize_current_labels()
            elif choice == 's':
                # Pick points
                indices = pick_points(current_pcd)
                if not indices:
                    print("No points selected")
                    continue
                
                print(f"Selected {len(indices)} points")
                print("Assign class:")
                for name, idx in CLASS_MAP.items():
                    if name != 'unlabeled':
                        print(f"  {idx}: {name}")
                
                class_choice = input("Class number: ").strip()
                if class_choice.isdigit() and int(class_choice) in REVERSE_CLASS_MAP:
                    class_label = int(class_choice)
                    class_name = REVERSE_CLASS_MAP[class_label]
                    
                    # Apply labels
                    for idx in indices:
                        self.point_labels[idx] = class_label
                    
                    print(f"Labeled {len(indices)} points as {class_name}")
                    
                    # Update colors
                    colors = np.array([CLASS_COLORS[label] for label in self.point_labels])
                    current_pcd.colors = o3d.utility.Vector3dVector(colors)
                else:
                    print("Invalid class number")
    
    def label_by_spatial_clustering(self, method='height_layers'):
        """Automatic labeling using spatial clustering techniques"""
        print(f"\n=== SPATIAL CLUSTERING: {method.upper()} ===")
        
        if method == 'height_layers':
            # Layer-based labeling (common for pile-on-pallet scenarios)
            z_coords = self.points[:, 2]
            z_min, z_max = z_coords.min(), z_coords.max()
            height_range = z_max - z_min
            
            if height_range > 2.0:  # Multi-layer structure
                # Bottom layer (likely pallets/ground)
                bottom_threshold = z_min + 0.3  # 30cm from bottom
                bottom_mask = z_coords <= bottom_threshold
                
                # Top layer (likely piles)  
                top_threshold = z_max - 0.5  # Within 50cm of top
                top_mask = z_coords >= top_threshold
                
                # Middle layer (supports/structure)
                middle_mask = ~(bottom_mask | top_mask)
                
                self.point_labels[bottom_mask] = CLASS_MAP['pallet']
                self.point_labels[top_mask] = CLASS_MAP['pile'] 
                self.point_labels[middle_mask] = CLASS_MAP['support']
                
                print(f"Bottom layer (pallet): {bottom_mask.sum()} points")
                print(f"Middle layer (support): {middle_mask.sum()} points") 
                print(f"Top layer (pile): {top_mask.sum()} points")
                
        elif method == 'density_based':
            # Use local point density to infer object types
            from sklearn.neighbors import NearestNeighbors
            
            # Calculate local density
            nbrs = NearestNeighbors(n_neighbors=10, algorithm='auto').fit(self.points)
            distances, indices = nbrs.kneighbors(self.points)
            local_density = 1.0 / (distances.mean(axis=1) + 1e-6)
            
            # High density = pile material, low density = structure
            density_threshold = np.percentile(local_density, 70)
            high_density_mask = local_density > density_threshold
            
            self.point_labels[high_density_mask] = CLASS_MAP['pile']
            self.point_labels[~high_density_mask] = CLASS_MAP['support']
            
            print(f"High density (pile): {high_density_mask.sum()} points")
            print(f"Low density (support): {(~high_density_mask).sum()} points")
    
    def save_labels(self):
        """Save the current point labels"""
        LABELS_DIR.mkdir(exist_ok=True)
        np.save(self.label_file, self.point_labels)
        
        # Create metadata
        unique_labels, counts = np.unique(self.point_labels, return_counts=True)
        label_distribution = {}
        for label, count in zip(unique_labels, counts):
            class_name = REVERSE_CLASS_MAP[label]
            label_distribution[class_name] = {
                'count': int(count),
                'percentage': float(count / self.num_points * 100)
            }
        
        metadata = {
            'cluster_name': self.cluster_name,
            'num_points': self.num_points,
            'stats': self.stats,
            'label_distribution': label_distribution,
            'labeled_date': str(pd.Timestamp.now())
        }
        
        return metadata

# %%
def interactive_mixed_labeling_session():
    """Main labeling session for mixed clusters"""
    
    # Load existing metadata
    metadata_path = LABELS_DIR / METADATA_FILE
    if metadata_path.exists():
        with open(metadata_path, 'r') as f:
            all_metadata = json.load(f)
        print(f"Loaded existing metadata for {len(all_metadata)} clusters")
    else:
        all_metadata = {}
    
    # Get cluster files
    cluster_files = sorted(list(CLUSTER_DIR.glob("*.ply")))
    if not cluster_files:
        print(f"No cluster files found in {CLUSTER_DIR}")
        return
    
    print(f"Found {len(cluster_files)} cluster files")
    
    current_idx = 0
    while current_idx < len(cluster_files):
        cluster_file = cluster_files[current_idx]
        
        try:
            # Check if cluster file is valid
            test_pcd = o3d.io.read_point_cloud(str(cluster_file))
            test_points = np.asarray(test_pcd.points)
            
            if len(test_points) == 0:
                print(f"WARNING: {cluster_file.name} is empty, skipping...")
                current_idx += 1
                continue
                
            # Initialize labeler
            labeler = ClusterLabeler(cluster_file)
            
            print(f"\n{'='*60}")
            print(f"Cluster {current_idx + 1}/{len(cluster_files)}: {labeler.cluster_name}")
            print(f"Points: {labeler.num_points}, Height: {labeler.stats['height']:.2f}m")
            print(f"Dimensions: {labeler.stats['width_x']:.1f} x {labeler.stats['width_y']:.1f} x {labeler.stats['height']:.1f}m")
            
            # Add quick preview option
            print("\nOptions:")
            print("  v: View cluster (recommended first step)")
            print("  a: Auto-label (quick start)")
            print("  m: Manual labeling menu")
            print("  s: Skip this cluster")
            
            initial_choice = input("Initial choice: ").strip().lower()
            
            if initial_choice == 's':
                current_idx += 1
                continue
            elif initial_choice == 'v':
                labeler.visualize_current_labels()
            elif initial_choice == 'a':
                # Quick auto-labeling for small clusters
                if labeler.stats['height'] > 1.5:
                    labeler.label_by_spatial_clustering('height_layers')
                else:
                    # Single class for small/flat clusters
                    labeler.point_labels[:] = CLASS_MAP['pallet']
                print("Auto-labeled cluster")
                labeler.visualize_current_labels()
            
            # Main labeling loop
            while True:
                print(f"\nDetailed labeling options for {labeler.cluster_name}:")
                print("  1: Height-based threshold labeling")
                print("  2: Spatial clustering (height layers)")
                print("  3: Spatial clustering (density-based)")
                print("  4: Manual point selection")
                print("  5: View current labels")
                print("  6: Reset all labels to unlabeled")
                print("  7: Quick single-class labeling")
                print("  s: Save and next cluster")
                print("  n: Next cluster (without saving)")
                print("  p: Previous cluster")
                print("  q: Quit and save all")
                
                choice = input("Choice: ").strip().lower()
                
                if choice == 'q':
                    # Save current cluster and exit
                    cluster_metadata = labeler.save_labels()
                    all_metadata[labeler.cluster_name] = cluster_metadata
                    current_idx = len(cluster_files)  # Exit outer loop
                    break
                    
                elif choice == 's':
                    # Save and move to next
                    cluster_metadata = labeler.save_labels()
                    all_metadata[labeler.cluster_name] = cluster_metadata
                    print(f"✓ Saved labels for {labeler.cluster_name}")
                    current_idx += 1
                    break
                    
                elif choice == 'n':
                    current_idx += 1
                    break
                    
                elif choice == 'p':
                    current_idx = max(0, current_idx - 1)
                    break
                    
                elif choice == '1':
                    # Height threshold
                    try:
                        height = float(input("Height threshold (meters): "))
                        below_class = input("Class for points below threshold: ").strip()
                        above_class = input("Class for points above threshold: ").strip()
                        
                        if below_class in CLASS_MAP and above_class in CLASS_MAP:
                            labeler.label_by_height_threshold(height, below_class, above_class)
                        else:
                            print("Invalid class names")
                    except ValueError:
                        print("Invalid height value")
                        
                elif choice == '2':
                    labeler.label_by_spatial_clustering('height_layers')
                    
                elif choice == '3':
                    labeler.label_by_spatial_clustering('density_based')
                    
                elif choice == '4':
                    labeler.label_by_manual_selection()
                    
                elif choice == '5':
                    labeler.visualize_current_labels()
                    
                elif choice == '6':
                    labeler.point_labels[:] = CLASS_MAP['unlabeled']
                    print("Reset all labels to unlabeled")
                    
                elif choice == '7':
                    print("Available classes:")
                    for name, idx in CLASS_MAP.items():
                        if name != 'unlabeled':
                            print(f"  {name}")
                    class_name = input("Class name for entire cluster: ").strip()
                    if class_name in CLASS_MAP:
                        labeler.point_labels[:] = CLASS_MAP[class_name]
                        print(f"Labeled entire cluster as {class_name}")
                    else:
                        print("Invalid class name")
                    
                else:
                    print("Invalid choice")
                    
        except Exception as e:
            print(f"Error processing {cluster_file.name}: {e}")
            import traceback
            traceback.print_exc()
            current_idx += 1
            continue
    
    # Save final metadata
    with open(metadata_path, 'w') as f:
        json.dump(all_metadata, f, indent=2)
    
    print(f"\n{'='*60}")
    print("LABELING SESSION COMPLETE")
    print(f"Processed clusters: {len(all_metadata)}")
    
    # Summary statistics
    total_points = sum(meta['num_points'] for meta in all_metadata.values())
    class_totals = {}
    
    for meta in all_metadata.values():
        for class_name, data in meta['label_distribution'].items():
            if class_name not in class_totals:
                class_totals[class_name] = 0
            class_totals[class_name] += data['count']
    
    print(f"\nOverall label distribution ({total_points} total points):")
    for class_name, count in class_totals.items():
        percentage = (count / total_points) * 100
        print(f"  {class_name}: {count:,} points ({percentage:.1f}%)")

# %%
def batch_auto_label_all():
    """Automatically label all clusters with smart heuristics, flag complex ones for review"""
    
    LABELS_DIR.mkdir(exist_ok=True)
    cluster_files = sorted(list(CLUSTER_DIR.glob("*.ply")))
    
    if not cluster_files:
        print(f"No cluster files found in {CLUSTER_DIR}")
        return
    
    print(f"=== BATCH AUTO-LABELING {len(cluster_files)} CLUSTERS ===")
    
    auto_labeled = []
    needs_review = []
    errors = []
    
    for i, cluster_file in enumerate(cluster_files):
        cluster_name = cluster_file.stem
        
        try:
            # Load cluster
            pcd = o3d.io.read_point_cloud(str(cluster_file))
            points = np.asarray(pcd.points)
            
            if len(points) == 0:
                print(f"⚠️  {cluster_name}: Empty cluster - skipping")
                errors.append((cluster_name, "empty"))
                continue
            
            # Calculate stats
            height = points[:, 2].max() - points[:, 2].min()
            num_points = len(points)
            
            # Smart auto-labeling for pile buffer regions
            if height > 3.0 and num_points > 150:
                # Large pile - use height layers
                point_labels = auto_label_height_layers(points)
                category = "multi-layer"
                
            elif height > 2.0 and num_points > 80:
                # Medium pile - mostly pile material with some ground
                point_labels = auto_label_pile_with_ground(points, height)
                category = "pile+ground"
                
            elif height > 1.5 and num_points > 50:
                # Small pile - likely mostly pile material
                point_labels = np.full(num_points, CLASS_MAP['pile'], dtype=np.int32)
                category = "pile"
                
            elif height < 0.8:
                # Flat area - likely ground/equipment
                point_labels = np.full(num_points, CLASS_MAP['ground'], dtype=np.int32)
                category = "ground"
                
            else:
                # Ambiguous case - flag for manual review
                needs_review.append((cluster_name, height, num_points, "ambiguous"))
                continue
            
            # Save labels
            label_file = LABELS_DIR / f"{cluster_name}.npy"
            np.save(label_file, point_labels)
            
            auto_labeled.append((cluster_name, category, height, num_points))
            
            if i % 50 == 0:
                print(f"Processed {i+1}/{len(cluster_files)} clusters...")
                
        except Exception as e:
            errors.append((cluster_name, str(e)))
            continue
    
    # Save metadata
    metadata = {}
    for cluster_name, category, height, num_points in auto_labeled:
        unique_labels, counts = np.unique(np.load(LABELS_DIR / f"{cluster_name}.npy"), return_counts=True)
        label_dist = {}
        for label, count in zip(unique_labels, counts):
            class_name = REVERSE_CLASS_MAP[label]
            label_dist[class_name] = {'count': int(count), 'percentage': float(count/num_points*100)}
        
        metadata[cluster_name] = {
            'auto_labeled': True,
            'category': category,
            'num_points': num_points,
            'height': height,
            'label_distribution': label_dist,
            'labeled_date': str(pd.Timestamp.now())
        }
    
    metadata_path = LABELS_DIR / METADATA_FILE
    with open(metadata_path, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    # Print summary
    print(f"\n{'='*60}")
    print("BATCH AUTO-LABELING COMPLETE")
    print(f"✅ Auto-labeled: {len(auto_labeled)} clusters")
    print(f"⚠️  Needs review: {len(needs_review)} clusters") 
    print(f"❌ Errors: {len(errors)} clusters")
    
    # Show auto-labeling breakdown
    categories = {}
    for _, category, _, _ in auto_labeled:
        categories[category] = categories.get(category, 0) + 1
    
    print(f"\nAuto-labeling breakdown:")
    for category, count in categories.items():
        print(f"  {category}: {count} clusters")
    
    # Show clusters that need manual review
    if needs_review:
        print(f"\n⚠️  CLUSTERS NEEDING MANUAL REVIEW:")
        for cluster_name, height, num_points, reason in needs_review:
            print(f"  {cluster_name}: h={height:.1f}m, pts={num_points}, reason={reason}")
        
        print(f"\nTo manually label these {len(needs_review)} clusters, run:")
        print("interactive_mixed_labeling_session()")
    
    if errors:
        print(f"\n❌ CLUSTERS WITH ERRORS:")
        for cluster_name, error in errors:
            print(f"  {cluster_name}: {error}")
    
    return auto_labeled, needs_review, errors

def auto_label_height_layers(points):
    """Auto-label using height layers for complex piles"""
    z_coords = points[:, 2]
    z_min, z_max = z_coords.min(), z_coords.max()
    height_range = z_max - z_min
    
    # Bottom 20% = ground
    bottom_threshold = z_min + 0.2 * height_range
    # Top 60% = pile material  
    top_threshold = z_min + 0.4 * height_range
    
    point_labels = np.full(len(points), CLASS_MAP['support'], dtype=np.int32)  # default middle
    point_labels[z_coords <= bottom_threshold] = CLASS_MAP['ground']
    point_labels[z_coords >= top_threshold] = CLASS_MAP['pile']
    
    return point_labels

def auto_label_pile_with_ground(points, height):
    """Auto-label pile material with ground base"""
    z_coords = points[:, 2]
    z_min = z_coords.min()
    
    # Bottom 30cm = ground, rest = pile
    ground_threshold = z_min + min(0.3, height * 0.2)
    
    point_labels = np.full(len(points), CLASS_MAP['pile'], dtype=np.int32)
    point_labels[z_coords <= ground_threshold] = CLASS_MAP['ground']
    
    return point_labels

if __name__ == "__main__":
    print("Choose labeling method:")
    print("1. Batch auto-label all clusters (recommended)")
    print("2. Interactive labeling session")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        batch_auto_label_all()
    elif choice == "2":
        interactive_mixed_labeling_session()
    else:
        print("Invalid choice")