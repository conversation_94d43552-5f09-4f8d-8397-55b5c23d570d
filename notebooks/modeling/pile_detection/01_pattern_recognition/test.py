what is this complicated code # %% [markdown]
# # PointNet++ Semantic Segmentation for Pile Clusters
# 
# This notebook implements **PointNet++** for semantic segmentation within DBSCAN clusters to classify points into:
# - **pile**: Main foundation pile structure
# - **support**: Support structures/scaffolding
# - **pallet**: Storage pallets
# - **non-pile**: Ground, debris, other objects
# 
# **PointNet++** provides hierarchical feature learning and better geometric understanding compared to PointNet.
# 
# **Author**: Preetam Balijepalli  
# **Date**: July 2025  
# **Project**: As-Built Foundation Analysis - Semantic Segmentation Stage

# %%
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import open3d as o3d
from pathlib import Path
import json
import matplotlib.pyplot as plt
from sklearn.metrics import confusion_matrix, classification_report
import seaborn as sns
from datetime import datetime
import pickle
import warnings
warnings.filterwarnings('ignore')

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# %% [markdown]
# ## 1. PointNet++ Architecture Components

# %%
def square_distance(src, dst):
    """Calculate Euclidean distance between each two points"""
    B, N, _ = src.shape
    _, M, _ = dst.shape
    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))
    dist += torch.sum(src ** 2, -1).view(B, N, 1)
    dist += torch.sum(dst ** 2, -1).view(B, 1, M)
    return dist

def index_points(points, idx):
    """Index points according to the indices"""
    device = points.device
    B = points.shape[0]
    view_shape = list(idx.shape)
    view_shape[1:] = [1] * (len(view_shape) - 1)
    repeat_shape = list(idx.shape)
    repeat_shape[0] = 1
    batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)
    new_points = points[batch_indices, idx, :]
    return new_points

def farthest_point_sample(xyz, npoint):
    """Farthest Point Sampling"""
    device = xyz.device
    B, N, C = xyz.shape
    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)
    distance = torch.ones(B, N).to(device) * 1e10
    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)
    batch_indices = torch.arange(B, dtype=torch.long).to(device)
    
    for i in range(npoint):
        centroids[:, i] = farthest
        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)
        dist = torch.sum((xyz - centroid) ** 2, -1)
        mask = dist < distance
        distance[mask] = dist[mask]
        farthest = torch.max(distance, -1)[1]
    
    return centroids

def query_ball_point(radius, nsample, xyz, new_xyz):
    """Ball query"""
    device = xyz.device
    B, N, C = xyz.shape
    _, S, _ = new_xyz.shape
    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])
    sqrdists = square_distance(new_xyz, xyz)
    group_idx[sqrdists > radius ** 2] = N
    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]
    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])
    mask = group_idx == N
    group_idx[mask] = group_first[mask]
    return group_idx

def sample_and_group(npoint, radius, nsample, xyz, points, returnfps=False):
    """Sample and group points"""
    B, N, C = xyz.shape
    S = npoint
    
    fps_idx = farthest_point_sample(xyz, npoint)  # [B, npoint]
    new_xyz = index_points(xyz, fps_idx)
    idx = query_ball_point(radius, nsample, xyz, new_xyz)
    grouped_xyz = index_points(xyz, idx)  # [B, npoint, nsample, C]
    grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)
    
    if points is not None:
        grouped_points = index_points(points, idx)
        new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)  # [B, npoint, nsample, C+D]
    else:
        new_points = grouped_xyz_norm
    
    if returnfps:
        return new_xyz, new_points, grouped_xyz, fps_idx
    else:
        return new_xyz, new_points

def sample_and_group_all(xyz, points):
    """Group all points"""
    device = xyz.device
    B, N, C = xyz.shape
    new_xyz = torch.zeros(B, 1, C).to(device)
    grouped_xyz = xyz.view(B, 1, N, C)
    if points is not None:
        new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)
    else:
        new_points = grouped_xyz
    return new_xyz, new_points

class PointNetSetAbstraction(nn.Module):
    """PointNet Set Abstraction Layer"""
    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):
        super(PointNetSetAbstraction, self).__init__()
        self.npoint = npoint
        self.radius = radius
        self.nsample = nsample
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm2d(out_channel))
            last_channel = out_channel
        self.group_all = group_all

    def forward(self, xyz, points):
        """
        Input:
            xyz: input points position data, [B, C, N]
            points: input points data, [B, D, N]
        Return:
            new_xyz: sampled points position data, [B, C, S]
            new_points_concat: sample points feature data, [B, D', S]
        """
        xyz = xyz.permute(0, 2, 1)
        if points is not None:
            points = points.permute(0, 2, 1)

        if self.group_all:
            new_xyz, new_points = sample_and_group_all(xyz, points)
        else:
            new_xyz, new_points = sample_and_group(self.npoint, self.radius, self.nsample, xyz, points)
        
        # new_xyz: sampled points position data, [B, npoint, C]
        # new_points: sampled points data, [B, npoint, nsample, C+D]
        new_points = new_points.permute(0, 3, 2, 1)  # [B, C+D, nsample, npoint]
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))

        new_points = torch.max(new_points, 2)[0]
        new_xyz = new_xyz.permute(0, 2, 1)
        return new_xyz, new_points

class PointNetFeaturePropagation(nn.Module):
    """Feature Propagation Layer for PointNet++"""
    def __init__(self, in_channel, mlp):
        super(PointNetFeaturePropagation, self).__init__()
        self.mlp_convs = nn.ModuleList()
        self.mlp_bns = nn.ModuleList()
        last_channel = in_channel
        for out_channel in mlp:
            self.mlp_convs.append(nn.Conv1d(last_channel, out_channel, 1))
            self.mlp_bns.append(nn.BatchNorm1d(out_channel))
            last_channel = out_channel

    def forward(self, xyz1, xyz2, points1, points2):
        """
        Input:
            xyz1: input points position data, [B, C, N]
            xyz2: sampled input points position data, [B, C, S]
            points1: input points data, [B, D, N]
            points2: input points data, [B, D, S]
        Return:
            new_points: upsampled points data, [B, D', N]
        """
        xyz1 = xyz1.permute(0, 2, 1)
        xyz2 = xyz2.permute(0, 2, 1)

        points2 = points2.permute(0, 2, 1)
        B, N, C = xyz1.shape
        _, S, _ = xyz2.shape

        if S == 1:
            interpolated_points = points2.repeat(1, N, 1)
        else:
            dists = square_distance(xyz1, xyz2)
            dists, idx = dists.sort(dim=-1)
            dists, idx = dists[:, :, :3], idx[:, :, :3]  # [B, N, 3]

            dist_recip = 1.0 / (dists + 1e-8)
            norm = torch.sum(dist_recip, dim=2, keepdim=True)
            weight = dist_recip / norm
            interpolated_points = torch.sum(index_points(points2, idx) * weight.view(B, N, 3, 1), dim=2)

        if points1 is not None:
            points1 = points1.permute(0, 2, 1)
            new_points = torch.cat([points1, interpolated_points], dim=-1)
        else:
            new_points = interpolated_points

        new_points = new_points.permute(0, 2, 1)
        for i, conv in enumerate(self.mlp_convs):
            bn = self.mlp_bns[i]
            new_points = F.relu(bn(conv(new_points)))
        return new_points

class PointNetPlusPlusSegmentation(nn.Module):
    """PointNet++ for Semantic Segmentation"""
    def __init__(self, num_classes=4):
        super(PointNetPlusPlusSegmentation, self).__init__()
        self.num_classes = num_classes
        
        # Set Abstraction layers
        self.sa1 = PointNetSetAbstraction(npoint=512, radius=0.2, nsample=32, in_channel=3, mlp=[64, 64, 128], group_all=False)
        self.sa2 = PointNetSetAbstraction(npoint=128, radius=0.4, nsample=64, in_channel=128 + 3, mlp=[128, 128, 256], group_all=False)
        self.sa3 = PointNetSetAbstraction(npoint=None, radius=None, nsample=None, in_channel=256 + 3, mlp=[256, 512, 1024], group_all=True)
        
        # Feature Propagation layers
        self.fp3 = PointNetFeaturePropagation(in_channel=1280, mlp=[256, 256])
        self.fp2 = PointNetFeaturePropagation(in_channel=384, mlp=[256, 128])
        self.fp1 = PointNetFeaturePropagation(in_channel=128, mlp=[128, 128, 128])
        
        # Classification head
        self.conv1 = nn.Conv1d(128, 128, 1)
        self.bn1 = nn.BatchNorm1d(128)
        self.drop1 = nn.Dropout(0.5)
        self.conv2 = nn.Conv1d(128, num_classes, 1)

    def forward(self, xyz):
        B, _, N = xyz.shape
        
        # Set Abstraction layers
        l1_xyz, l1_points = self.sa1(xyz, None)
        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)
        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)
        
        # Feature Propagation layers
        l2_points = self.fp3(l2_xyz, l3_xyz, l2_points, l3_points)
        l1_points = self.fp2(l1_xyz, l2_xyz, l1_points, l2_points)
        l0_points = self.fp1(xyz, l1_xyz, None, l1_points)
        
        # Classification
        feat = F.relu(self.bn1(self.conv1(l0_points)))
        feat = self.drop1(feat)
        feat = self.conv2(feat)
        feat = F.log_softmax(feat, dim=1)
        
        return feat.permute(0, 2, 1)

# %% [markdown]
# ## 2. Dataset Class for Pile Clusters (Enhanced for PointNet++)

# %%
class PileClusterDataset(Dataset):
    """Enhanced dataset for PointNet++ with better preprocessing"""
    
    def __init__(self, cluster_dir, labels_dir=None, num_points=4096, augment=False):
        self.cluster_dir = Path(cluster_dir)
        self.labels_dir = Path(labels_dir) if labels_dir else None
        self.num_points = num_points
        self.augment = augment
        
        # Class mapping
        self.class_map = {
            'pile': 0,
            'support': 1, 
            'pallet': 2,
            'non-pile': 3
        }
        self.num_classes = len(self.class_map)
        
        # Load cluster files
        self.cluster_files = list(self.cluster_dir.glob("*.ply"))
        print(f"Found {len(self.cluster_files)} cluster files")
        
    def __len__(self):
        return len(self.cluster_files)
    
    def __getitem__(self, idx):
        cluster_file = self.cluster_files[idx]
        
        # Load point cloud
        pcd = o3d.io.read_point_cloud(str(cluster_file))
        points = np.asarray(pcd.points)
        
        # Enhanced preprocessing for PointNet++
        points = self.process_points_enhanced(points)
        
        # Load labels if available
        if self.labels_dir:
            label_file = self.labels_dir / f"{cluster_file.stem}_labels.npy"
            if label_file.exists():
                labels = np.load(label_file)
                labels = self.process_labels(labels, len(points))
            else:
                labels = np.full(len(points), self.class_map['non-pile'])  # default
        else:
            labels = np.full(len(points), -1)  # unlabeled
            
        # Data augmentation
        if self.augment:
            points = self.augment_points(points)
            
        return {
            'points': torch.FloatTensor(points).transpose(1, 0),  # [3, N]
            'labels': torch.LongTensor(labels),
            'cluster_id': cluster_file.stem
        }
    
    def process_points_enhanced(self, points):
        """Enhanced point processing for PointNet++"""
        # Remove outliers first
        if len(points) > 100:
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(points)
            pcd, _ = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
            points = np.asarray(pcd.points)
        
        # Sample or pad points
        if len(points) >= self.num_points:
            # Use farthest point sampling for better coverage
            indices = self.farthest_point_sampling_numpy(points, self.num_points)
            points = points[indices]
        else:
            # Pad with jittered copies
            n_repeats = (self.num_points // len(points)) + 1
            repeated_points = np.tile(points, (n_repeats, 1))
            
            # Add small jitter to avoid identical points
            jitter = np.random.normal(0, 0.01, repeated_points.shape)
            repeated_points = repeated_points + jitter
            
            points = repeated_points[:self.num_points]
            
        # Normalize to unit sphere (better for PointNet++)
        centroid = np.mean(points, axis=0)
        points = points - centroid
        max_dist = np.max(np.linalg.norm(points, axis=1))
        if max_dist > 0:
            points = points / max_dist
            
        return points
    
    def farthest_point_sampling_numpy(self, points, num_samples):
        """Numpy implementation of farthest point sampling"""
        n_points = len(points)
        selected = np.zeros(num_samples, dtype=int)
        distances = np.full(n_points, np.inf)
        
        # Start with random point
        selected[0] = np.random.randint(0, n_points)
        
        for i in range(1, num_samples):
            last_selected = selected[i-1]
            # Calculate distances to last selected point
            dists = np.linalg.norm(points - points[last_selected], axis=1)
            # Update minimum distances
            distances = np.minimum(distances, dists)
            # Select farthest point
            selected[i] = np.argmax(distances)
            
        return selected
    
    def process_labels(self, labels, target_length):
        """Process labels to match point count"""
        if len(labels) >= target_length:
            # Use same indices as point sampling
            indices = self.farthest_point_sampling_numpy(
                np.arange(len(labels)).reshape(-1, 1), target_length
            )
            return labels[indices]
        else:
            repeats = target_length // len(labels) + 1
            labels = np.tile(labels, repeats)[:target_length]
            return labels
    
    def augment_points(self, points):
        """Enhanced data augmentation for construction scenes"""
        # Random rotation around Z-axis (vertical structures)
        angle = np.random.uniform(0, 2 * np.pi)
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation = np.array([[cos_a, -sin_a, 0],
                           [sin_a, cos_a, 0],
                           [0, 0, 1]])
        points = points @ rotation.T
        
        # Random scaling (realistic for construction tolerance)
        scale = np.random.uniform(0.95, 1.05)
        points = points * scale
        
        # Random jittering (sensor noise simulation)
        noise = np.random.normal(0, 0.01, points.shape)
        points = points + noise
        
        # Random point dropout (occlusion simulation)
        if np.random.rand() < 0.1:  # 10% chance
            dropout_ratio = np.random.uniform(0.05, 0.15)
            keep_indices = np.random.choice(
                len(points), 
                int(len(points) * (1 - dropout_ratio)), 
                replace=False
            )
            # Pad back to original size by repeating some points
            missing = len(points) - len(keep_indices)
            if missing > 0:
                repeat_indices = np.random.choice(keep_indices, missing)
                keep_indices = np.concatenate([keep_indices, repeat_indices])
            points = points[keep_indices]
        
        return points

# %% [markdown]
# ## 3. Enhanced Training Functions

# %%
def train_epoch(model, dataloader, optimizer, criterion, device):
    """Train for one epoch with PointNet++"""
    model.train()
    running_loss = 0.0
    correct = 0
    total = 0
    
    for batch_idx, data in enumerate(dataloader):
        points = data['points'].to(device)  # [B, 3, N]
        labels = data['labels'].to(device)  # [B, N]
        
        optimizer.zero_grad()
        
        pred = model(points)  # [B, N, num_classes]
        pred = pred.view(-1, model.num_classes)
        labels = labels.view(-1)
        
        # Filter out invalid labels
        valid_mask = labels >= 0
        if valid_mask.sum() == 0:
            continue
            
        pred = pred[valid_mask]
        labels = labels[valid_mask]
        
        loss = criterion(pred, labels)
        loss.backward()
        optimizer.step()
        
        running_loss += loss.item()
        _, predicted = torch.max(pred, 1)
        total += labels.size(0)
        correct += (predicted == labels).sum().item()
        
        if batch_idx % 10 == 0:
            print(f'Batch {batch_idx}/{len(dataloader)}, Loss: {loss.item():.4f}')
    
    accuracy = 100. * correct / total if total > 0 else 0
    avg_loss = running_loss / len(dataloader)
    
    return avg_loss, accuracy

def validate_epoch(model, dataloader, criterion, device):
    """Validate for one epoch"""
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        pred = model(points_tensor)  # [1, N, num_classes]
        pred = pred.squeeze(0)  # [N, num_classes]
        predicted_labels = torch.argmax(pred, dim=1).cpu().numpy()
        confidence_scores = torch.softmax(pred, dim=1).cpu().numpy()
    
    return original_points, processed_points, predicted_labels, confidence_scores

def analyze_pile_structure(points, labels, confidence_scores, class_map):
    """Analyze the structure of segmented pile"""
    results = {}
    
    # Class distribution
    unique_labels, counts = np.unique(labels, return_counts=True)
    total_points = len(labels)
    
    class_distribution = {}
    for label, count in zip(unique_labels, counts):
        class_name = list(class_map.keys())[list(class_map.values()).index(label)]
        percentage = (count / total_points) * 100
        class_distribution[class_name] = {
            'count': int(count),
            'percentage': float(percentage)
        }
    
    # Extract pile points specifically
    pile_mask = labels == class_map['pile']
    if pile_mask.sum() > 0:
        pile_points = points[pile_mask]
        pile_confidence = confidence_scores[pile_mask, class_map['pile']]
        
        # Pile geometry analysis
        pile_centroid = np.mean(pile_points, axis=0)
        pile_height = np.max(pile_points[:, 2]) - np.min(pile_points[:, 2])
        
        # Estimate pile diameter (using 2D projection)
        pile_2d = pile_points[:, :2] - pile_centroid[:2]
        pile_radius_estimates = np.linalg.norm(pile_2d, axis=1)
        pile_diameter = 2 * np.percentile(pile_radius_estimates, 95)  # 95th percentile for robustness
        
        # Quality metrics
        avg_confidence = np.mean(pile_confidence)
        min_confidence = np.min(pile_confidence)
        
        results['pile_analysis'] = {
            'centroid': pile_centroid.tolist(),
            'height': float(pile_height),
            'estimated_diameter': float(pile_diameter),
            'avg_confidence': float(avg_confidence),
            'min_confidence': float(min_confidence),
            'quality_score': float(avg_confidence * (pile_mask.sum() / total_points))
        }
    else:
        results['pile_analysis'] = None
    
    results['class_distribution'] = class_distribution
    results['total_points'] = total_points
    
    return results

def visualize_segmentation_enhanced(points, labels, confidence_scores, class_map, title="PointNet++ Segmentation"):
    """Enhanced visualization with confidence scores"""
    # Create color map with confidence-based intensity
    base_colors = {
        0: np.array([1, 0, 0]),      # pile - red
        1: np.array([0, 1, 0]),      # support - green  
        2: np.array([0, 0, 1]),      # pallet - blue
        3: np.array([0.5, 0.5, 0.5]) # non-pile - gray
    }
    
    point_colors = []
    for i, label in enumerate(labels):
        base_color = base_colors.get(label, np.array([0, 0, 0]))
        confidence = confidence_scores[i, label]
        # Modulate color intensity by confidence
        final_color = base_color * (0.3 + 0.7 * confidence)
        point_colors.append(final_color)
    
    point_colors = np.array(point_colors)
    
    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.colors = o3d.utility.Vector3dVector(point_colors)
    
    # Add coordinate frame for reference
    coord_frame = o3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0)
    
    # Visualize
    o3d.visualization.draw_geometries([pcd, coord_frame], window_name=title)
    
    return pcd

def extract_pile_only_pointcloud(points, labels, confidence_scores, class_map, min_confidence=0.7):
    """Extract high-confidence pile points only"""
    pile_label = class_map['pile']
    pile_mask = labels == pile_label
    confidence_mask = confidence_scores[:, pile_label] >= min_confidence
    
    final_mask = pile_mask & confidence_mask
    
    if final_mask.sum() == 0:
        return None
    
    pile_points = points[final_mask]
    pile_confidences = confidence_scores[final_mask, pile_label]
    
    # Create clean pile point cloud
    pile_pcd = o3d.geometry.PointCloud()
    pile_pcd.points = o3d.utility.Vector3dVector(pile_points)
    
    # Color by confidence (red gradient)
    colors = np.zeros((len(pile_points), 3))
    colors[:, 0] = pile_confidences  # Red channel
    pile_pcd.colors = o3d.utility.Vector3dVector(colors)
    
    return pile_pcd, pile_points, pile_confidences

def batch_analyze_all_clusters(cluster_dir, model_path, output_dir="pointnet_plus_results"):
    """Comprehensive analysis of all clusters with PointNet++"""
    
    # Load model
    checkpoint = torch.load(model_path, map_location=device)
    class_map = checkpoint['class_map']
    num_points = checkpoint.get('num_points', 4096)
    
    model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Create output directories
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    (output_path / "segmented_clusters").mkdir(exist_ok=True)
    (output_path / "pile_only").mkdir(exist_ok=True)
    (output_path / "analysis_reports").mkdir(exist_ok=True)
    
    # Process all clusters
    cluster_files = list(Path(cluster_dir).glob("*.ply"))
    all_results = []
    pile_centroids = []
    
    print(f"Processing {len(cluster_files)} clusters with PointNet++...")
    
    for i, cluster_file in enumerate(cluster_files):
        print(f"Processing {cluster_file.name} ({i+1}/{len(cluster_files)})...")
        
        try:
            # Segment cluster
            original_points, processed_points, predicted_labels, confidence_scores = segment_cluster_pointnet_plus(
                model, str(cluster_file), class_map, num_points=num_points, device=device
            )
            
            # Analyze results
            analysis = analyze_pile_structure(processed_points, predicted_labels, confidence_scores, class_map)
            analysis['cluster_id'] = cluster_file.stem
            analysis['original_point_count'] = len(original_points)
            analysis['processed_point_count'] = len(processed_points)
            
            # Save segmented cluster
            segmented_pcd = visualize_segmentation_enhanced(
                processed_points, predicted_labels, confidence_scores, class_map,
                title=f"Cluster {cluster_file.stem}"
            )
            
            segmented_file = output_path / "segmented_clusters" / f"{cluster_file.stem}_segmented.ply"
            o3d.io.write_point_cloud(str(segmented_file), segmented_pcd)
            
            # Extract and save pile-only points
            pile_result = extract_pile_only_pointcloud(
                processed_points, predicted_labels, confidence_scores, class_map
            )
            
            if pile_result is not None:
                pile_pcd, pile_points, pile_confidences = pile_result
                pile_file = output_path / "pile_only" / f"{cluster_file.stem}_pile_only.ply"
                o3d.io.write_point_cloud(str(pile_file), pile_pcd)
                
                # Add to centroids for final summary
                if analysis['pile_analysis']:
                    pile_centroids.append({
                        'cluster_id': cluster_file.stem,
                        'centroid': analysis['pile_analysis']['centroid'],
                        'height': analysis['pile_analysis']['height'],
                        'diameter': analysis['pile_analysis']['estimated_diameter'],
                        'quality_score': analysis['pile_analysis']['quality_score']
                    })
            
            # Save labels and confidence scores
            np.save(output_path / "analysis_reports" / f"{cluster_file.stem}_labels.npy", predicted_labels)
            np.save(output_path / "analysis_reports" / f"{cluster_file.stem}_confidence.npy", confidence_scores)
            
            all_results.append(analysis)
            
        except Exception as e:
            print(f"Error processing {cluster_file.name}: {str(e)}")
            continue
    
    # Generate summary report
    summary = generate_pile_summary_report(all_results, pile_centroids)
    
    # Save all results
    with open(output_path / "detailed_analysis.json", 'w') as f:
        json.dump(all_results, f, indent=2)
    
    with open(output_path / "summary_report.json", 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Save pile centroids for GIS/CAD export
    if pile_centroids:
        centroids_array = np.array([p['centroid'] for p in pile_centroids])
        np.savetxt(output_path / "pile_centroids_final.csv", centroids_array, 
                  delimiter=",", header="x,y,z", comments="")
        
        # Create pile summary CSV
        pile_df_data = []
        for p in pile_centroids:
            pile_df_data.append({
                'cluster_id': p['cluster_id'],
                'x': p['centroid'][0],
                'y': p['centroid'][1], 
                'z': p['centroid'][2],
                'height': p['height'],
                'diameter': p['diameter'],
                'quality_score': p['quality_score']
            })
        
        import pandas as pd
        pile_df = pd.DataFrame(pile_df_data)
        pile_df.to_csv(output_path / "pile_inventory.csv", index=False)
    
    print(f"\nAnalysis complete!")
    print(f"- Processed {len(all_results)} clusters")
    print(f"- Identified {len(pile_centroids)} high-quality piles")
    print(f"- Results saved in {output_dir}/")
    
    return all_results, pile_centroids

def generate_pile_summary_report(all_results, pile_centroids):
    """Generate comprehensive summary report"""
    summary = {
        'analysis_date': datetime.now().isoformat(),
        'total_clusters_processed': len(all_results),
        'total_piles_detected': len(pile_centroids),
        'detection_rate': len(pile_centroids) / len(all_results) if all_results else 0
    }
    
    if pile_centroids:
        heights = [p['height'] for p in pile_centroids]
        diameters = [p['diameter'] for p in pile_centroids]
        quality_scores = [p['quality_score'] for p in pile_centroids]
        
        summary['pile_statistics'] = {
            'height': {
                'mean': float(np.mean(heights)),
                'std': float(np.std(heights)),
                'min': float(np.min(heights)),
                'max': float(np.max(heights)),
                'median': float(np.median(heights))
            },
            'diameter': {
                'mean': float(np.mean(diameters)),
                'std': float(np.std(diameters)),
                'min': float(np.min(diameters)),
                'max': float(np.max(diameters)),
                'median': float(np.median(diameters))
            },
            'quality_scores': {
                'mean': float(np.mean(quality_scores)),
                'std': float(np.std(quality_scores)),
                'min': float(np.min(quality_scores)),
                'max': float(np.max(quality_scores))
            }
        }
        
        # Quality categories
        high_quality = sum(1 for q in quality_scores if q >= 0.8)
        medium_quality = sum(1 for q in quality_scores if 0.6 <= q < 0.8)
        low_quality = sum(1 for q in quality_scores if q < 0.6)
        
        summary['quality_distribution'] = {
            'high_quality': high_quality,
            'medium_quality': medium_quality,
            'low_quality': low_quality
        }
    
    # Class distribution across all clusters
    all_class_counts = {}
    for result in all_results:
        for class_name, data in result.get('class_distribution', {}).items():
            if class_name not in all_class_counts:
                all_class_counts[class_name] = 0
            all_class_counts[class_name] += data['count']
    
    total_points = sum(all_class_counts.values())
    summary['overall_class_distribution'] = {
        class_name: {
            'count': count,
            'percentage': (count / total_points * 100) if total_points > 0 else 0
        }
        for class_name, count in all_class_counts.items()
    }
    
    return summary

# %% [markdown]
# ## 6. Usage Example and Main Pipeline

# %%
if __name__ == "__main__":
    print("=== POINTNET++ PILE SEGMENTATION PIPELINE ===")
    print("Enhanced with hierarchical feature learning and better geometric understanding")
    
    # Check if we have labeled data for training
    if Path("cluster_labels").exists():
        print("Found labeled data - training PointNet++ model...")
        model, class_map = train_pointnet_plus_segmentation()
    else:
        print("No labeled data found. Please create 'cluster_labels' directory with labeled data.")
        print("For now, loading pre-trained model if available...")
        
        if Path("best_pointnet_plus_pile_segmentation.pth").exists():
            checkpoint = torch.load("best_pointnet_plus_pile_segmentation.pth", map_location=device)
            class_map = checkpoint['class_map']
            model = PointNetPlusPlusSegmentation(num_classes=len(class_map)).to(device)
            model.load_state_dict(checkpoint['model_state_dict'])
            print("Loaded pre-trained PointNet++ model")
        else:
            print("No pre-trained model found. Please train first.")
            exit()
    
    # Run comprehensive analysis on all clusters
    if Path("dbscan_clusters").exists():
        print("Running PointNet++ segmentation on all clusters...")
        all_results, pile_centroids = batch_analyze_all_clusters(
            cluster_dir="dbscan_clusters",
            model_path="best_pointnet_plus_pile_segmentation.pth"
        )
        
        print(f"\n=== FINAL RESULTS ===")
        print(f"✓ Processed {len(all_results)} clusters")
        print(f"✓ Detected {len(pile_centroids)} piles")
        print(f"✓ Detection rate: {len(pile_centroids)/len(all_results)*100:.1f}%")
        
        if pile_centroids:
            heights = [p['height'] for p in pile_centroids]
            diameters = [p['diameter'] for p in pile_centroids]
            print(f"✓ Average pile height: {np.mean(heights):.2f}m ± {np.std(heights):.2f}m")
            print(f"✓ Average pile diameter: {np.mean(diameters):.2f}m ± {np.std(diameters):.2f}m")
        
        print("✓ Results saved in 'pointnet_plus_results' directory")
        print("  - Segmented clusters: pointnet_plus_results/segmented_clusters/")
        print("  - Pile-only point clouds: pointnet_plus_results/pile_only/")
        print("  - Analysis reports: pointnet_plus_results/analysis_reports/")
        print("  - Summary: pointnet_plus_results/summary_report.json")
        print("  - Pile inventory: pointnet_plus_results/pile_inventory.csv")
        
    else:
        print("No clusters found. Please run DBSCAN clustering first.")

# %% [markdown]
# ## 7. Advanced Analysis Functions

# %%
def compare_with_design_plans(detected_piles, design_pile_locations, tolerance=2.0):
    """Compare detected piles with design plans"""
    if not design_pile_locations:
        return None
    
    matches = []
    unmatched_detected = []
    unmatched_design = list(range(len(design_pile_locations)))
    
    for i, detected in enumerate(detected_piles):
        detected_pos = np.array(detected['centroid'][:2])  # X,Y only
        
        best_match = None
        min_distance = float('inf')
        
        for j, design_pos in enumerate(design_pile_locations):
            design_pos = np.array(design_pos[:2])
            distance = np.linalg.norm(detected_pos - design_pos)
            
            if distance < tolerance and distance < min_distance:
                min_distance = distance
                best_match = j
        
        if best_match is not None:
            matches.append({
                'detected_index': i,
                'design_index': best_match,
                'distance_error': min_distance,
                'detected_pile': detected,
                'design_location': design_pile_locations[best_match]
            })
            unmatched_design.remove(best_match)
        else:
            unmatched_detected.append(i)
    
    return {
        'matches': matches,
        'unmatched_detected': unmatched_detected,
        'unmatched_design': unmatched_design,
        'match_rate': len(matches) / len(design_pile_locations) if design_pile_locations else 0,
        'false_positive_rate': len(unmatched_detected) / len(detected_piles) if detected_piles else 0
    }

def generate_construction_report(results_dir="pointnet_plus_results"):
    """Generate comprehensive construction monitoring report"""
    results_path = Path(results_dir)
    
    # Load analysis results
    with open(results_path / "summary_report.json", 'r') as f:
        summary = json.load(f)
    
    # Create report
    report = {
        'title': 'As-Built Foundation Pile Analysis Report',
        'generated_date': datetime.now().isoformat(),
        'analysis_summary': summary,
        'quality_assessment': assess_overall_quality(summary),
        'recommendations': generate_recommendations(summary)
    }
    
    # Save report
    with open(results_path / "construction_report.json", 'w') as f:
        json.dump(report, f, indent=2)
    
    return report

def assess_overall_quality(summary):
    """Assess overall quality of pile detection and construction"""
    assessment = {'overall_score': 0, 'issues': [], 'strengths': []}
    
    if 'pile_statistics' in summary:
        stats = summary['pile_statistics']
        
        # Check height consistency
        height_cv = stats['height']['std'] / stats['height']['mean']
        if height_cv < 0.1:
            assessment['strengths'].append("Consistent pile heights")
        elif height_cv > 0.3:
            assessment['issues'].append("High variation in pile heights")
        
        # Check diameter consistency  
        diameter_cv = stats['diameter']['std'] / stats['diameter']['mean']
        if diameter_cv < 0.15:
            assessment['strengths'].append("Consistent pile diameters")
        elif diameter_cv > 0.4:
            assessment['issues'].append("High variation in pile diameters")
        
        # Quality scores
        avg_quality = stats['quality_scores']['mean']
        if avg_quality > 0.8:
            assessment['strengths'].append("High detection confidence")
            assessment['overall_score'] += 30
        elif avg_quality < 0.6:
            assessment['issues'].append("Low detection confidence")
    
    # Detection rate
    detection_rate = summary.get('detection_rate', 0)
    if detection_rate > 0.9:
        assessment['strengths'].append("High pile detection rate")
        assessment['overall_score'] += 25
    elif detection_rate < 0.7:
        assessment['issues'].append("Low pile detection rate")
    
    # Class distribution
    if 'overall_class_distribution' in summary:
        pile_percentage = summary['overall_class_distribution'].get('pile', {}).get('percentage', 0)
        if pile_percentage > 30:
            assessment['strengths'].append("Good pile-to-clutter ratio")
            assessment['overall_score'] += 20
        elif pile_percentage < 15:
            assessment['issues'].append("High clutter in scanned area")
    
    assessment['overall_score'] = min(100, assessment['overall_score'])
    return assessment

def generate_recommendations(summary):
    """Generate actionable recommendations"""
    recommendations = []
    
    if 'quality_distribution' in summary:
        low_quality = summary['quality_distribution'].get('low_quality', 0)
        total_piles = sum(summary['quality_distribution'].values())
        
        if low_quality / total_piles > 0.2:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Quality Control',
                'recommendation': 'Re-scan areas with low-quality pile detections',
                'details': f'{low_quality} piles have low confidence scores and may need verification'
            })
    
    if summary.get('detection_rate', 1) < 0.8:
        recommendations.append({
            'priority': 'MEDIUM',
            'category': 'Coverage',
            'recommendation': 'Improve scanning coverage in areas with missing pile detections',
            'details': 'Some expected pile locations may not have been adequately captured'
        })
    
    if 'pile_statistics' in summary:
        height_std = summary['pile_statistics']['height']['std']
        if height_std > 0.5:  # More than 0.5m variation
            recommendations.append({
                'priority': 'MEDIUM',
                'category': 'Construction Quality',
                'recommendation': 'Review pile installation consistency',
                'details': f'Pile heights vary by ±{height_std:.2f}m, which may indicate installation issues'
            })
    
    return recommendations

print("PointNet++ implementation complete with enhanced analysis capabilities!")
print("Key improvements over PointNet:")
print("- Hierarchical feature learning")
print("- Better handling of point density variations")
print("- Improved geometric understanding")
print("- Enhanced preprocessing with FPS sampling")
print("- Comprehensive pile analysis and reporting")
        for data in dataloader:
            points = data['points'].to(device)
            labels = data['labels'].to(device)
            
            pred = model(points)
            pred = pred.view(-1, model.num_classes)
            labels = labels.view(-1)
            
            # Filter out invalid labels
            valid_mask = labels >= 0
            if valid_mask.sum() == 0:
                continue
                
            pred = pred[valid_mask]
            labels = labels[valid_mask]
            
            loss = criterion(pred, labels)
            
            running_loss += loss.item()
            _, predicted = torch.max(pred, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    accuracy = 100. * correct / total if total > 0 else 0
    avg_loss = running_loss / len(dataloader)
    
    return avg_loss, accuracy, all_preds, all_labels

# %% [markdown]
# ## 4. Main Training Function

# %%
def train_pointnet_plus_segmentation():
    """Main training function for PointNet++"""
    
    # Parameters - increased for PointNet++
    CLUSTER_DIR = "dbscan_clusters"
    LABELS_DIR = "cluster_labels"
    NUM_POINTS = 4096  # Increased for better performance
    BATCH_SIZE = 4     # Reduced due to memory requirements
    NUM_EPOCHS = 150   # Increased for better convergence
    LEARNING_RATE = 0.001
    
    # Create dataset
    dataset = PileClusterDataset(
        cluster_dir=CLUSTER_DIR,
        labels_dir=LABELS_DIR,
        num_points=NUM_POINTS,
        augment=True
    )
    
    # Split dataset
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=2)
    
    # Initialize model
    model = PointNetPlusPlusSegmentation(num_classes=dataset.num_classes).to(device)
    
    # Loss and optimizer with class weighting
    class_weights = torch.tensor([1.0, 1.2, 1.1, 0.8]).to(device)  # Emphasize pile/support classes
    criterion = nn.NLLLoss(weight=class_weights)
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=NUM_EPOCHS)
    
    # Training loop
    train_losses = []
    train_accuracies = []
    val_losses = []
    val_accuracies = []
    
    best_val_acc = 0.0
    
    for epoch in range(NUM_EPOCHS):
        print(f'\nEpoch {epoch+1}/{NUM_EPOCHS}')
        print('-' * 50)
        
        # Train
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        train_losses.append(train_loss)
        train_accuracies.append(train_acc)
        
        # Validate
        val_loss, val_acc, val_preds, val_labels = validate_epoch(model, val_loader, criterion, device)
        val_losses.append(val_loss)
        val_accuracies.append(val_acc)
        
        scheduler.step()
        
        print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
        print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
        print(f'Learning Rate: {scheduler.get_last_lr()[0]:.6f}')
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'class_map': dataset.class_map,
                'num_points': NUM_POINTS
            }, 'best_pointnet_plus_pile_segmentation.pth')
            print(f'New best model saved with val acc: {val_acc:.2f}%')
        
        # Generate classification report every 20 epochs
        if epoch % 20 == 0 and len(val_preds) > 0:
            class_names = list(dataset.class_map.keys())
            print("\nValidation Classification Report:")
            print(classification_report(val_labels, val_preds, target_names=class_names, zero_division=0))
    
    # Plot training curves
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training and Validation Loss')
    
    plt.subplot(1, 3, 2)
    plt.plot(train_accuracies, label='Train Acc')
    plt.plot(val_accuracies, label='Val Acc')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.title('Training and Validation Accuracy')
    
    plt.subplot(1, 3, 3)
    if len(val_preds) > 0:
        cm = confusion_matrix(val_labels, val_preds)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title('Confusion Matrix (Final Validation)')
        plt.xlabel('Predicted')
        plt.ylabel('Actual')
    
    plt.tight_layout()
    plt.savefig('pointnet_plus_training_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return model, dataset.class_map

# %% [markdown]
# ## 5. Enhanced Inference and Analysis

# %%
def segment_cluster_pointnet_plus(model, cluster_file, class_map, num_points=4096, device='cpu'):
    """Segment a single cluster with PointNet++"""
    model.eval()
    
    # Load point cloud
    pcd = o3d.io.read_point_cloud(cluster_file)
    original_points = np.asarray(pcd.points)
    
    # Process points with enhanced preprocessing
    dataset = PileClusterDataset(".", num_points=num_points)
    processed_points = dataset.process_points_enhanced(original_points)
    
    # Convert to tensor
    points_tensor = torch.FloatTensor(processed_points).transpose(1, 0).unsqueeze(0).to(device)
    
    with torch.no_grad():


